from rest_framework import serializers
from .models import School, SchoolBranch

class SchoolBasicSerializer(serializers.ModelSerializer):
    """Basic serializer for School model with essential fields only"""
    class Meta:
        model = School
        fields = ['id', 'name', 'code', 'registration_number', 'is_active']

class SchoolSerializer(serializers.ModelSerializer):
    # Add a field to show the number of branches for each school
    branch_count = serializers.SerializerMethodField()

    def get_branch_count(self, obj):
        return obj.branch.count()

    class Meta:
        model = School
        fields = '__all__'

class SchoolBranchSerializer(serializers.ModelSerializer):
    # Add school name for easier reference
    school_name = serializers.ReadOnlyField(source='school.name')

    class Meta:
        model = SchoolBranch
        fields = '__all__'
