import { useState } from "react";
import { useNavigate, Link, UseLocation } from "react-router-dom";
import { DropdownItem } from "../ui/dropdown/DropdownItem";
import { Dropdown } from "../ui/dropdown/Dropdown";
import { authService } from "../../services/authService.ts";
import { useAuth } from "../../context/AuthContext";
import { useSchool } from "../../contexts/SchoolContext";

interface UserDropdownProps {
  className?: string;
}

export default function UserDropdown({ className }: UserDropdownProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [isLoggingOut, setIsLoggingOut] = useState(false);
  const navigate = useNavigate();
  const { user, logout: authLogout } = useAuth();
  const { schools, branches, selectedSchool, selectedBranch, setSelectedSchool, setSelectedBranch } = useSchool();

  // Check if user is super user
  const isSuperUser = user?.is_superuser || false;

  const toggleDropdown = () => setIsOpen(!isOpen);
  const closeDropdown = () => setIsOpen(false);

  const handleLogout = async () => {
    setIsLoggingOut(true);
    try {
      // Call logout function, which already clears tokens
      // The logout function now always succeeds, even if there's no refresh token
      await authService.logout();

      // Update auth context
      authLogout();

      // Redirect to sign-in page
      navigate("/signin", { replace: true });
    } catch (error) {
      console.error("Logout error:", error);

      // Even if there's an error, update auth context and redirect
      authLogout();
      navigate("/signin", { replace: true });
    } finally {
      setIsLoggingOut(false);
      closeDropdown();
    }
  };

  // Handle school switching for super users
  const handleSchoolSwitch = (school: any) => {
    setSelectedSchool(school);
    closeDropdown();
  };

  const handleBranchSwitch = (branch: any) => {
    setSelectedBranch(branch);
    closeDropdown();
  };

  // Base dropdown items
  const baseDropdownItems = [
    {
      label: "Edit Profile",
      icon: (
        <svg
          className="fill-gray-500 group-hover:fill-gray-700 dark:fill-gray-400 dark:group-hover:fill-gray-300"
          width="24" height="24" viewBox="0 0 24 24"
        >
          <path fillRule="evenodd" clipRule="evenodd" d="M12 3.5C7.30558 3.5 3.5 7.30558 3.5 12C3.5 14.1526 4.3002 16.1184 5.61936 17.616C6.17279 15.3096 8.24852 13.5955 10.7246 13.5955H13.2746C15.7509 13.5955 17.8268 15.31 18.38 17.6167C19.6996 16.119 20.5 14.153 20.5 12C20.5 7.30558 16.6944 3.5 12 3.5Z" />
        </svg>
      ),
      to: "/",
      onClick: closeDropdown
    }
  ];

  // Super user specific items
  const superUserItems = isSuperUser ? [
    {
      label: "System Administration",
      icon: (
        <svg
          className="fill-gray-500 group-hover:fill-gray-700 dark:fill-gray-400 dark:group-hover:fill-gray-300"
          width="24" height="24" viewBox="0 0 24 24"
        >
          <path fillRule="evenodd" clipRule="evenodd" d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z" />
        </svg>
      ),
      to: "/admin",
      onClick: closeDropdown
    },
    {
      label: "Manage Schools",
      icon: (
        <svg
          className="fill-gray-500 group-hover:fill-gray-700 dark:fill-gray-400 dark:group-hover:fill-gray-300"
          width="24" height="24" viewBox="0 0 24 24"
        >
          <path fillRule="evenodd" clipRule="evenodd" d="M12 3L2 12h3v8h6v-6h2v6h6v-8h3L12 3z" />
        </svg>
      ),
      to: "/admin/schools",
      onClick: closeDropdown
    },
    {
      label: "License Management",
      icon: (
        <svg
          className="fill-gray-500 group-hover:fill-gray-700 dark:fill-gray-400 dark:group-hover:fill-gray-300"
          width="24" height="24" viewBox="0 0 24 24"
        >
          <path fillRule="evenodd" clipRule="evenodd" d="M12 1L3 5v6c0 5.55 3.84 10.74 9 12 5.16-1.26 9-6.45 9-12V5l-9-4z" />
        </svg>
      ),
      to: "/admin/licenses",
      onClick: closeDropdown
    }
  ] : [];

  const dropdownItems = [...baseDropdownItems, ...superUserItems];

  return (
    <div className={`relative ${className}`}>
      <button
        type="button"
        onClick={toggleDropdown}
        className="flex items-center text-gray-700 dropdown-toggle dark:text-gray-400"
        aria-expanded={isOpen ? 'true' : 'false'}
        aria-haspopup="true"
        aria-label="User menu"
      >
        <span className="mr-3 overflow-hidden rounded-full h-11 w-11">
          <img
            src={user?.avatar || "/images/user/avatar.png"}
            alt={user?.first_name || "User"}
            className="object-cover w-full h-full"
          />
        </span>
        <span className="block mr-1 font-medium text-theme-sm">
          {user?.first_name || "User"}
        </span>
        <svg
          className={`stroke-gray-500 dark:stroke-gray-400 transition-transform duration-200 ${
            isOpen ? "rotate-180" : ""
          }`}
          width="18"
          height="20"
          viewBox="0 0 18 20"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M4.3125 8.65625L9 13.3437L13.6875 8.65625"
            stroke="currentColor"
            strokeWidth="1.5"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
        </svg>
      </button>

      <Dropdown
        isOpen={isOpen}
        onClose={closeDropdown}
        className="absolute right-0 mt-[17px] w-[260px] rounded-2xl border border-gray-200 bg-white p-3 shadow-theme-lg dark:border-gray-800 dark:bg-gray-dark"
      >
        <div>
          <span className="block font-medium text-gray-700 text-theme-sm dark:text-gray-400">
            {user?.first_name} {user?.last_name}
          </span>
          <span className="mt-0.5 block text-theme-xs text-gray-500 dark:text-gray-400">
            {user?.email}
          </span>
          {isSuperUser && (
            <span className="mt-1 inline-block px-2 py-1 text-xs font-medium text-blue-600 bg-blue-100 rounded-full dark:text-blue-400 dark:bg-blue-900/30">
              Super User
            </span>
          )}
        </div>

        {/* Super User School/Branch Switching */}
        {isSuperUser && (
          <div className="pt-3 pb-3 border-b border-gray-200 dark:border-gray-800">
            <div className="mb-2">
              <label className="block text-xs font-medium text-gray-500 dark:text-gray-400 mb-1">
                Current School
              </label>
              <select
                value={selectedSchool?.id || ''}
                onChange={(e) => {
                  const school = schools.find(s => s.id === Number(e.target.value));
                  if (school) handleSchoolSwitch(school);
                }}
                className="w-full px-2 py-1 text-sm border border-gray-300 rounded-md dark:border-gray-600 dark:bg-gray-700 dark:text-gray-200"
                aria-label="Select current school"
              >
                <option value="">Select School</option>
                {schools.map((school) => (
                  <option key={school.id} value={school.id}>
                    {school.name}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-xs font-medium text-gray-500 dark:text-gray-400 mb-1">
                Current Branch
              </label>
              <select
                value={selectedBranch?.id || ''}
                onChange={(e) => {
                  const branch = branches.find(b => b.id === Number(e.target.value));
                  if (branch) handleBranchSwitch(branch);
                }}
                className="w-full px-2 py-1 text-sm border border-gray-300 rounded-md dark:border-gray-600 dark:bg-gray-700 dark:text-gray-200"
                disabled={!selectedSchool}
                aria-label="Select current branch"
              >
                <option value="">Select Branch</option>
                {branches
                  .filter(branch => !selectedSchool || branch.school === selectedSchool.id)
                  .map((branch) => (
                    <option key={branch.id} value={branch.id}>
                      {branch.name}
                    </option>
                  ))}
              </select>
            </div>
          </div>
        )}

        <ul className="flex flex-col gap-1 pt-4 pb-3 border-b border-gray-200 dark:border-gray-800">
          {dropdownItems.map((item, index) => (
            <li key={index}>
              <DropdownItem
                tag={Link}
                to={item.to}
                onItemClick={item.onClick}
                className="flex items-center gap-3 px-3 py-2 font-medium text-gray-700 rounded-lg group text-theme-sm hover:bg-gray-100"
              >
                {item.icon}
                {item.label}
              </DropdownItem>
            </li>
          ))}
        </ul>

        <ul className="flex flex-col gap-1 pt-3">
          <li>
            <DropdownItem
              tag="button"
              disabled={isLoggingOut}
              onItemClick={handleLogout}
              className="flex w-full items-center gap-3 px-3 py-2 font-medium text-red-600 rounded-lg group text-theme-sm hover:bg-red-100"
            >
              <svg
                className="fill-red-500 group-hover:fill-red-700"
                width="24" height="24" viewBox="0 0 24 24"
              >
                <path fillRule="evenodd" clipRule="evenodd" d="M14.707 15.293a1 1 0 010 1.414l-3.999 4a1 1 0 01-1.414-1.414L12.586 16H4a1 1 0 110-2h8.586l-3.292-3.293a1 1 0 011.414-1.414l3.999 4z" />
              </svg>
              {isLoggingOut ? "Signing out..." : "Sign out"}
            </DropdownItem>
          </li>
        </ul>
      </Dropdown>
    </div>
  );
}
