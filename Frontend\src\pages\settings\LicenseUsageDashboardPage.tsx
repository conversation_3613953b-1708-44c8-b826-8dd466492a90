import React, { useState, useEffect, useCallback, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import PageBreadcrumb from '../../components/common/PageBreadCrumb';
import PageMeta from '../../components/common/PageMeta';
import licenseService, { License } from '../../services/licenseService';
import statisticsService, { SchoolStatistics } from '../../services/statisticsService';
import SchoolSelector from '../../components/admin/SchoolSelector';
import { useAuth } from '../../context/AuthContext';
import debounce from '../../utils/debounce';

const LicenseUsageDashboardPage: React.FC = () => {
  const [license, setLicense] = useState<License | null>(null);
  const [statistics, setStatistics] = useState<SchoolStatistics | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isStatsLoading, setIsStatsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [statsError, setStatsError] = useState(false);

  // School selection state for super users
  const [selectedSchoolId, setSelectedSchoolId] = useState<number | null>(null);
  const [selectedSchoolName, setSelectedSchoolName] = useState<string>('');
  const { user } = useAuth();
  const navigate = useNavigate();

  // Create a stable reference to the debounced fetch function that persists across renders
  const debouncedFetchRef = useRef<ReturnType<typeof debounce> | null>(null);

  // Handle school selection for super users
  const handleSchoolSelect = useCallback((schoolId: number, schoolName: string) => {
    // Skip if the same school is selected again
    if (schoolId === selectedSchoolId) return;

    // Update state immediately for UI feedback
    setSelectedSchoolId(schoolId);
    setSelectedSchoolName(schoolName);

    // Cancel any pending debounced calls
    if (debouncedFetchRef.current) {
      debouncedFetchRef.current.cancel();
    }

    // Create a new debounced function or reuse existing one
    if (!debouncedFetchRef.current) {
      debouncedFetchRef.current = debounce((id: number) => {
        fetchData(id);
      }, 300);
    }

    // Call the debounced function
    debouncedFetchRef.current(schoolId);
  }, [selectedSchoolId]);

  // Fetch license and statistics data
  const fetchData = async (schoolId?: number) => {
    try {
      setIsLoading(true);
      setError(null);

      // Fetch license data first - use schoolId if provided (for super users)
      try {
        const licenseData = schoolId
          ? await licenseService.getLicenseBySchoolId(schoolId)
          : await licenseService.getMyLicense();
        setLicense(licenseData);

        // Note: We allow license mismatch between school ID and license school ID
      } catch {
        // Silently handle license fetch errors
        setError('Failed to load license information. Using default data.');
      }

      // Fetch statistics separately to handle potential errors
      setIsStatsLoading(true);
      try {
        const statsData = schoolId
          ? await statisticsService.getSchoolStatisticsById(schoolId)
          : await statisticsService.getSchoolStatistics();
        setStatistics(statsData);
        setStatsError(false);

        // Note: Empty statistics (zero counts) will be handled by the UI
      } catch {
        // Silently handle statistics fetch errors
        // Initialize with zeros instead of mock data
        setStatistics({
          student_count: 0,
          staff_count: 0,
          branch_count: 0,
          active_users_count: 0,
          total_users_count: 0
        });
        setStatsError(true);
        // Don't set the main error, just show a warning in the stats section
      } finally {
        setIsStatsLoading(false);
      }
    } catch {
      // Silently handle any other errors
      setError('Failed to load license usage information. Please try again later.');
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    // Redirect non-admin users
    if (user && !user.is_admin && !user.is_superuser) {
      navigate('/dashboard');
      return;
    }

    // Initial data fetch
    fetchData();
    // We intentionally exclude fetchData from dependencies to avoid infinite loops
    // as it would cause the effect to run again whenever fetchData is redefined
  }, [user, navigate]);

  // Calculate days until expiry
  const calculateDaysUntilExpiry = () => {
    if (!license) return 0;

    const today = new Date();
    const expiryDate = new Date(license.expiry_date);
    return Math.ceil((expiryDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));
  };

  // Calculate usage percentages
  const calculateUsagePercentages = () => {
    if (!license || !statistics) {
      return {
        studentPercent: 0,
        staffPercent: 0,
        branchPercent: 0
      };
    }

    // Add safety checks for license properties
    const maxStudents = license.max_students || 1;
    const maxStaff = license.max_staff || 1;
    const maxBranches = license.max_branches || 1;

    return {
      studentPercent: Math.min(100, Math.round((statistics.student_count / maxStudents) * 100)),
      staffPercent: Math.min(100, Math.round((statistics.staff_count / maxStaff) * 100)),
      branchPercent: Math.min(100, Math.round((statistics.branch_count / maxBranches) * 100))
    };
  };

  const daysUntilExpiry = license ? calculateDaysUntilExpiry() : 0;
  const { studentPercent, staffPercent, branchPercent } = calculateUsagePercentages();

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64 dark:bg-gray-900">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary dark:border-indigo-500"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded dark:bg-red-900/20 dark:border-red-800 dark:text-red-400">
        <p>{error}</p>
      </div>
    );
  }

  if (!license || !statistics) {
    return (
      <>
        <PageMeta
          title="License Usage Dashboard | ShuleXcel"
          description="View detailed license usage statistics and metrics."
        />
        <PageBreadcrumb pageTitle="License Usage Dashboard" />

        <div className="container mx-auto px-4 py-6 dark:bg-gray-900">
          <div className="bg-yellow-50 border border-yellow-200 text-yellow-700 px-4 py-3 rounded dark:bg-yellow-900/20 dark:border-yellow-800 dark:text-yellow-400">
            <p>No license or usage data found. Please ensure you have a valid license configured.</p>
          </div>
        </div>
      </>
    );
  }

  return (
    <>
      <PageMeta
        title="License Usage Dashboard | ShuleXcel"
        description="View detailed license usage statistics and metrics."
      />
      <PageBreadcrumb pageTitle="License Usage Dashboard" />

      <div className="container mx-auto px-4 py-6 dark:bg-gray-900">
        {/* License Overview Card */}
        <div className="bg-white shadow rounded-lg p-6 mb-6 dark:bg-gray-800 dark:shadow-gray-900/30">
          <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6">
            <div>
              <h1 className="text-2xl font-bold text-gray-900 dark:text-white">License Usage Dashboard</h1>
              <p className="text-gray-600 dark:text-gray-400 mt-1">
                Monitor your license usage and subscription status
              </p>
            </div>
            <div className="mt-4 md:mt-0 flex space-x-3">
              <button
                type="button"
                className="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 dark:bg-indigo-700 dark:hover:bg-indigo-800"
                onClick={() => navigate('/settings/license')}
              >
                View License Details
              </button>
              {user?.is_superuser && (
                <button
                  type="button"
                  className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 dark:bg-green-700 dark:hover:bg-green-800"
                  onClick={() => navigate('/settings/licenses/manage')}
                >
                  Manage Licenses
                </button>
              )}
            </div>
          </div>

          {/* School Selector for Super Users */}
          {user?.is_superuser && (
            <div className="mb-6 p-4 bg-indigo-50 rounded-lg border border-indigo-100 dark:bg-indigo-900/20 dark:border-indigo-800">
              <h2 className="text-lg font-semibold text-indigo-900 dark:text-indigo-300 mb-2">Select School</h2>
              <p className="text-sm text-indigo-700 dark:text-indigo-400 mb-4">
                As a system administrator, you can view license usage for any school in the system.
              </p>
              <div className="max-w-md">
                <SchoolSelector
                  onSchoolSelect={handleSchoolSelect}
                  selectedSchoolId={selectedSchoolId || undefined}
                  className="w-full"
                />
              </div>
              {selectedSchoolName && (
                <div className="mt-2 text-sm font-medium text-indigo-700 dark:text-indigo-400">
                  Currently viewing: {selectedSchoolName}
                </div>
              )}

              {/* API Status Notification */}
              {statsError && selectedSchoolId && (
                <div className="mt-4 bg-yellow-50 border-l-4 border-yellow-400 p-4 text-sm text-yellow-700 dark:bg-yellow-900/20 dark:border-yellow-700 dark:text-yellow-400">
                  <div className="flex">
                    <div className="flex-shrink-0">
                      <svg className="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                      </svg>
                    </div>
                    <div className="ml-3">
                      <p className="font-medium">Statistics Unavailable</p>
                      <p className="mt-1">Could not load detailed statistics for this school. Using default values.</p>
                      <p className="mt-1 text-xs">This may happen if the school is new or has no data yet.</p>
                    </div>
                  </div>
                </div>
              )}
            </div>
          )}

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div className="bg-gradient-to-br from-indigo-50 to-blue-50 p-6 rounded-lg border border-indigo-100 dark:from-indigo-900/30 dark:to-blue-900/30 dark:border-indigo-800">
              <h3 className="text-lg font-medium text-indigo-800 dark:text-indigo-300 mb-2">Package</h3>
              <p className="text-3xl font-bold text-indigo-700 dark:text-indigo-400">{license.package_name}</p>
              <p className="text-sm text-indigo-600 dark:text-indigo-500 mt-2">
                {license.subscription_status === 'ACTIVE' ? 'Active Subscription' : license.subscription_status}
              </p>
            </div>

            <div className={`bg-gradient-to-br p-6 rounded-lg border ${
              daysUntilExpiry <= 0 ? 'from-red-50 to-red-100 border-red-200 dark:from-red-900/30 dark:to-red-900/20 dark:border-red-800' :
              daysUntilExpiry <= 7 ? 'from-orange-50 to-orange-100 border-orange-200 dark:from-orange-900/30 dark:to-orange-900/20 dark:border-orange-800' :
              daysUntilExpiry <= 30 ? 'from-yellow-50 to-yellow-100 border-yellow-200 dark:from-yellow-900/30 dark:to-yellow-900/20 dark:border-yellow-800' :
              'from-green-50 to-green-100 border-green-200 dark:from-green-900/30 dark:to-green-900/20 dark:border-green-800'
            }`}>
              <h3 className="text-lg font-medium text-gray-800 dark:text-gray-300 mb-2">Expiry</h3>
              <p className={`text-3xl font-bold ${
                daysUntilExpiry <= 0 ? 'text-red-700 dark:text-red-400' :
                daysUntilExpiry <= 7 ? 'text-orange-700 dark:text-orange-400' :
                daysUntilExpiry <= 30 ? 'text-yellow-700 dark:text-yellow-400' :
                'text-green-700 dark:text-green-400'
              }`}>
                {daysUntilExpiry <= 0 ? 'Expired' : `${daysUntilExpiry} days`}
              </p>
              <p className="text-sm text-gray-600 dark:text-gray-400 mt-2">
                {new Date(license.expiry_date).toLocaleDateString()}
              </p>
            </div>

            <div className="bg-gradient-to-br from-purple-50 to-indigo-50 p-6 rounded-lg border border-purple-100 dark:from-purple-900/30 dark:to-indigo-900/30 dark:border-purple-800">
              <h3 className="text-lg font-medium text-purple-800 dark:text-purple-300 mb-2">License Key</h3>
              <p className="text-xl font-mono font-bold text-purple-700 dark:text-purple-400 truncate">
                {license.formatted_license_key}
              </p>
              <p className="text-sm text-purple-600 dark:text-purple-500 mt-2">
                Created: {new Date(license.created_at).toLocaleDateString()}
              </p>
            </div>

            <div className="bg-gradient-to-br from-blue-50 to-cyan-50 p-6 rounded-lg border border-blue-100 dark:from-blue-900/30 dark:to-cyan-900/30 dark:border-blue-800">
              <h3 className="text-lg font-medium text-blue-800 dark:text-blue-300 mb-2">Total Users</h3>
              <p className="text-3xl font-bold text-blue-700 dark:text-blue-400">
                {statistics.total_users_count}
              </p>
              <p className="text-sm text-blue-600 dark:text-blue-500 mt-2">
                Active: {statistics.active_users_count} users
              </p>
            </div>
          </div>
        </div>

        {/* Usage Metrics */}
        <div className="mb-6">
          {isStatsLoading ? (
            <div className="bg-white shadow rounded-lg p-6 dark:bg-gray-800">
              <div className="flex justify-center items-center py-6">
                <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary dark:border-indigo-500"></div>
              </div>
            </div>
          ) : statsError ? (
            <div className="bg-white shadow rounded-lg p-6 dark:bg-gray-800">
              <div className="bg-yellow-50 border border-yellow-200 text-yellow-700 px-4 py-3 rounded dark:bg-yellow-900/20 dark:border-yellow-800 dark:text-yellow-400">
                <p>Could not load usage statistics. Using default values.</p>
              </div>
            </div>
          ) : (
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              {/* Student Usage */}
              <div className="bg-white shadow rounded-lg p-6 dark:bg-gray-800">
                <div className="flex justify-between items-center mb-4">
                  <h2 className="text-xl font-semibold text-gray-800 dark:text-white">Student Usage</h2>
                  <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                    studentPercent >= 95 ? 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400' :
                    studentPercent >= 80 ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400' :
                    'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400'
                  }`}>
                    {studentPercent}%
                  </span>
                </div>

                <div className="relative pt-1">
                  <div className="flex mb-2 items-center justify-between">
                    <div>
                      <span className="text-xs font-semibold inline-block text-gray-600 dark:text-gray-400">
                        {statistics.student_count} of {license.max_students} students
                      </span>
                    </div>
                    <div className="text-right">
                      <span className="text-xs font-semibold inline-block text-gray-600 dark:text-gray-400">
                        {license.max_students - statistics.student_count} remaining
                      </span>
                    </div>
                  </div>
                  <div className="overflow-hidden h-2 mb-4 text-xs flex rounded bg-gray-200 dark:bg-gray-700">
                    <div
                      className={`shadow-none flex flex-col text-center whitespace-nowrap text-white justify-center w-[${studentPercent}%] ${
                        studentPercent >= 95 ? 'bg-red-500 dark:bg-red-600' :
                        studentPercent >= 80 ? 'bg-yellow-500 dark:bg-yellow-600' :
                        'bg-green-500 dark:bg-green-600'
                      }`}
                    ></div>
                  </div>
                </div>

                {studentPercent >= 95 && (
                  <div className="mt-4 bg-red-50 border-l-4 border-red-400 p-4 text-sm text-red-700 dark:bg-red-900/20 dark:border-red-700 dark:text-red-400">
                    <p>You have almost reached your student limit. Consider upgrading your license soon.</p>
                  </div>
                )}

                {studentPercent >= 80 && studentPercent < 95 && (
                  <div className="mt-4 bg-yellow-50 border-l-4 border-yellow-400 p-4 text-sm text-yellow-700 dark:bg-yellow-900/20 dark:border-yellow-700 dark:text-yellow-400">
                    <p>You are approaching your student limit. Plan accordingly.</p>
                  </div>
                )}
              </div>

              {/* Staff Usage */}
              <div className="bg-white shadow rounded-lg p-6 dark:bg-gray-800">
                <div className="flex justify-between items-center mb-4">
                  <h2 className="text-xl font-semibold text-gray-800 dark:text-white">Staff Usage</h2>
                  <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                    staffPercent >= 95 ? 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400' :
                    staffPercent >= 80 ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400' :
                    'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400'
                  }`}>
                    {staffPercent}%
                  </span>
                </div>

                <div className="relative pt-1">
                  <div className="flex mb-2 items-center justify-between">
                    <div>
                      <span className="text-xs font-semibold inline-block text-gray-600 dark:text-gray-400">
                        {statistics.staff_count} of {license.max_staff} staff
                      </span>
                    </div>
                    <div className="text-right">
                      <span className="text-xs font-semibold inline-block text-gray-600 dark:text-gray-400">
                        {license.max_staff - statistics.staff_count} remaining
                      </span>
                    </div>
                  </div>
                  <div className="overflow-hidden h-2 mb-4 text-xs flex rounded bg-gray-200 dark:bg-gray-700">
                    <div
                      className={`shadow-none flex flex-col text-center whitespace-nowrap text-white justify-center w-[${staffPercent}%] ${
                        staffPercent >= 95 ? 'bg-red-500 dark:bg-red-600' :
                        staffPercent >= 80 ? 'bg-yellow-500 dark:bg-yellow-600' :
                        'bg-green-500 dark:bg-green-600'
                      }`}
                    ></div>
                  </div>
                </div>

                {staffPercent >= 95 && (
                  <div className="mt-4 bg-red-50 border-l-4 border-red-400 p-4 text-sm text-red-700 dark:bg-red-900/20 dark:border-red-700 dark:text-red-400">
                    <p>You have almost reached your staff limit. Consider upgrading your license soon.</p>
                  </div>
                )}

                {staffPercent >= 80 && staffPercent < 95 && (
                  <div className="mt-4 bg-yellow-50 border-l-4 border-yellow-400 p-4 text-sm text-yellow-700 dark:bg-yellow-900/20 dark:border-yellow-700 dark:text-yellow-400">
                    <p>You are approaching your staff limit. Plan accordingly.</p>
                  </div>
                )}
              </div>

              {/* Branch Usage */}
              <div className="bg-white shadow rounded-lg p-6 dark:bg-gray-800">
                <div className="flex justify-between items-center mb-4">
                  <h2 className="text-xl font-semibold text-gray-800 dark:text-white">Branch Usage</h2>
                  <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                    branchPercent >= 95 ? 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400' :
                    branchPercent >= 80 ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400' :
                    'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400'
                  }`}>
                    {branchPercent}%
                  </span>
                </div>

                <div className="relative pt-1">
                  <div className="flex mb-2 items-center justify-between">
                    <div>
                      <span className="text-xs font-semibold inline-block text-gray-600 dark:text-gray-400">
                        {statistics.branch_count} of {license.max_branches} branches
                      </span>
                    </div>
                    <div className="text-right">
                      <span className="text-xs font-semibold inline-block text-gray-600 dark:text-gray-400">
                        {license.max_branches - statistics.branch_count} remaining
                      </span>
                    </div>
                  </div>
                  <div className="overflow-hidden h-2 mb-4 text-xs flex rounded bg-gray-200 dark:bg-gray-700">
                    <div
                      className={`shadow-none flex flex-col text-center whitespace-nowrap text-white justify-center w-[${branchPercent}%] ${
                        branchPercent >= 95 ? 'bg-red-500 dark:bg-red-600' :
                        branchPercent >= 80 ? 'bg-yellow-500 dark:bg-yellow-600' :
                        'bg-green-500 dark:bg-green-600'
                      }`}
                    ></div>
                  </div>
                </div>

                {branchPercent >= 95 && (
                  <div className="mt-4 bg-red-50 border-l-4 border-red-400 p-4 text-sm text-red-700 dark:bg-red-900/20 dark:border-red-700 dark:text-red-400">
                    <p>You have almost reached your branch limit. Consider upgrading your license soon.</p>
                  </div>
                )}

                {branchPercent >= 80 && branchPercent < 95 && (
                  <div className="mt-4 bg-yellow-50 border-l-4 border-yellow-400 p-4 text-sm text-yellow-700 dark:bg-yellow-900/20 dark:border-yellow-700 dark:text-yellow-400">
                    <p>You are approaching your branch limit. Plan accordingly.</p>
                  </div>
                )}
              </div>
            </div>
          )}
        </div>

        {/* User Activity */}
        <div className="bg-white shadow rounded-lg p-6 mb-6 dark:bg-gray-800">
          <h2 className="text-xl font-semibold text-gray-800 dark:text-white mb-4">User Activity</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div className="bg-gray-50 rounded-lg p-4 dark:bg-gray-700">
              <div className="flex items-center">
                <div className="flex-shrink-0 bg-blue-100 rounded-full p-3 dark:bg-blue-900/30">
                  <svg className="w-6 h-6 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                  </svg>
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Student Users</p>
                  <p className="text-lg font-semibold text-gray-900 dark:text-white">{statistics.student_users || 0}</p>
                </div>
              </div>
            </div>
            <div className="bg-gray-50 rounded-lg p-4 dark:bg-gray-700">
              <div className="flex items-center">
                <div className="flex-shrink-0 bg-green-100 rounded-full p-3 dark:bg-green-900/30">
                  <svg className="w-6 h-6 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                  </svg>
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Teacher Users</p>
                  <p className="text-lg font-semibold text-gray-900 dark:text-white">{statistics.teacher_users || 0}</p>
                </div>
              </div>
            </div>
            <div className="bg-gray-50 rounded-lg p-4 dark:bg-gray-700">
              <div className="flex items-center">
                <div className="flex-shrink-0 bg-purple-100 rounded-full p-3 dark:bg-purple-900/30">
                  <svg className="w-6 h-6 text-purple-600 dark:text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2 2v2m4 6h.01M5 20h14a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                  </svg>
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Staff Users</p>
                  <p className="text-lg font-semibold text-gray-900 dark:text-white">{statistics.staff_users || 0}</p>
                </div>
              </div>
            </div>
            <div className="bg-gray-50 rounded-lg p-4 dark:bg-gray-700">
              <div className="flex items-center">
                <div className="flex-shrink-0 bg-amber-100 rounded-full p-3 dark:bg-amber-900/30">
                  <svg className="w-6 h-6 text-amber-600 dark:text-amber-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Active Last 30 Days</p>
                  <p className="text-lg font-semibold text-gray-900 dark:text-white">{statistics.active_last_month || 0}</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Module Usage */}
        <div className="bg-white shadow rounded-lg p-6 dark:bg-gray-800">
          <h2 className="text-xl font-semibold text-gray-800 dark:text-white mb-4">Enabled Modules</h2>

          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
            {license.enabled_modules && Array.isArray(license.enabled_modules) ? license.enabled_modules.map((module, index) => {
              // Handle both string and object module formats
              const moduleKey = typeof module === 'string' ? module : module.code || index;
              const moduleName = typeof module === 'string' ? module : module.name || 'Unknown Module';

              return (
                <div key={moduleKey} className="border border-gray-200 rounded-lg p-4 bg-gray-50 dark:bg-gray-700 dark:border-gray-600">
                  <div className="flex items-center">
                    <div className="h-3 w-3 rounded-full bg-green-500 mr-2"></div>
                    <span className="text-gray-800 dark:text-gray-200 font-medium">{moduleName}</span>
                  </div>
                </div>
              );
            }) : (
              <div className="col-span-full text-center text-gray-500 dark:text-gray-400 py-8">
                No modules enabled or module data unavailable.
              </div>
            )}
          </div>

          <div className="mt-6 flex justify-end">
            <button
              type="button"
              className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 dark:bg-gray-700 dark:border-gray-600 dark:text-gray-200 dark:hover:bg-gray-600"
              onClick={() => navigate('/settings/license')}
            >
              Back to License Management
            </button>
          </div>
        </div>
      </div>
    </>
  );
};

export default LicenseUsageDashboardPage;
