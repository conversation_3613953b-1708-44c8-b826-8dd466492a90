import axios from 'axios';
import { API_URL } from '../config';
import { isLicenseEndpointMissing, markLicenseEndpointMissing } from '../utils/apiCache';

export interface License {
  id: number;
  school: number;
  school_name?: string; // Optional school name property
  package_type: string;
  package_name: string;
  subscription_status: string;
  is_active: boolean;
  start_date: string;
  expiry_date: string;
  max_students: number;
  max_staff: number;
  max_branches: number;
  custom_modules: string[] | null;
  enabled_modules: Array<string | Module>;
  formatted_license_key: string;
  created_at: string;
  updated_at: string;
  // Usage statistics
  current_students?: number;
  current_staff?: number;
  active_users?: number;
  total_users?: number;
}

export interface Package {
  code: string;
  name: string;
  description: string;
  modules: string[];
}

export interface Module {
  code: string;
  name: string;
  description: string;
  is_core: boolean;
  dependencies: string[];
  app_names: string[];
  status: string;
}

const licenseService = {
  /**
   * Get the current school's license
   */
  async getMyLicense(): Promise<License> {
    const token = localStorage.getItem('token');
    if (!token) {
      throw new Error('Authentication required');
    }

    try {
      const response = await axios.get(`${API_URL}/api/settings/licenses/my_license/`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      return response.data;
    } catch (error) {
      // Check if this is a super user without school assignment
      if (axios.isAxiosError(error) && error.response?.status === 404) {
        // For super users, return a special license object indicating they can manage all licenses
        const errorData = error.response.data;
        if (errorData?.is_superuser) {
          return {
            id: 0,
            school: 0,
            school_name: 'Super User Access',
            package_type: 'enterprise',
            package_name: 'Enterprise',
            subscription_status: 'ACTIVE',
            is_active: true,
            start_date: new Date().toISOString().split('T')[0],
            expiry_date: new Date(new Date().setFullYear(new Date().getFullYear() + 10)).toISOString().split('T')[0],
            max_students: 999999,
            max_staff: 999999,
            max_branches: 999999,
            custom_modules: null,
            enabled_modules: [],
            formatted_license_key: 'SUPER-USER-ACCESS',
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          };
        }
      }
      throw error;
    }
  },

  /**
   * Get all licenses (admin only)
   * @param schoolId Optional school ID to filter licenses by school
   */
  async getAllLicenses(schoolId?: number): Promise<License[]> {
    const token = localStorage.getItem('token');
    if (!token) {
      throw new Error('Authentication required');
    }

    // Build URL with query parameters if schoolId is provided
    const url = schoolId
      ? `${API_URL}/api/settings/licenses/?school=${schoolId}`
      : `${API_URL}/api/settings/licenses/`;

    const response = await axios.get(url, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });

    // Handle both array and object responses
    if (Array.isArray(response.data)) {
      return response.data;
    } else if (response.data.results && Array.isArray(response.data.results)) {
      // Handle paginated response
      return response.data.results;
    } else if (response.data && typeof response.data === 'object') {
      // Handle single object response
      return [response.data];
    }

    // Default to empty array if none of the above
    return [];
  },

  /**
   * Get license by school ID (for super users)
   * Note: Falls back to getMyLicense if the endpoint is not available
   */
  async getLicenseBySchoolId(schoolId: number): Promise<License> {
    const token = localStorage.getItem('token');
    if (!token) {
      throw new Error('Authentication required');
    }

    // Check if we already know this endpoint is missing
    if (isLicenseEndpointMissing(schoolId)) {
      // Skip the API call and go straight to fallback
      return this.getLicenseBySchoolFallback(schoolId);
    }

    try {
      // Try to get the school-specific license
      const response = await axios.get(`${API_URL}/api/settings/licenses/school/${schoolId}/`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      return response.data;
    } catch (error) {
      // Add to cache so we don't try this endpoint again
      markLicenseEndpointMissing(schoolId);
      // Silently use fallback without logging
      return this.getLicenseBySchoolFallback(schoolId);
    }
  },

  /**
   * Fallback method to get license by school ID when the endpoint is not available
   * @private
   */
  async getLicenseBySchoolFallback(schoolId: number): Promise<License> {
    try {
      // Fallback: Get all licenses and filter by school ID
      const allLicenses = await this.getAllLicenses();
      const schoolLicense = allLicenses.find(license => license.school === schoolId);

      if (schoolLicense) {
        return schoolLicense;
      }

      // If no license found for this school, use the user's license as fallback
      return await this.getMyLicense();
    } catch {
      // Silently handle fallback errors but still throw a user-friendly error
      throw new Error('Could not retrieve license information for this school');
    }
  },

  /**
   * Get license by ID
   */
  async getLicenseById(id: number): Promise<License> {
    const token = localStorage.getItem('token');
    if (!token) {
      throw new Error('Authentication required');
    }

    const response = await axios.get(`${API_URL}/api/settings/licenses/${id}/`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
    return response.data;
  },

  /**
   * Create a new license
   */
  async createLicense(licenseData: Partial<License>): Promise<License> {
    const token = localStorage.getItem('token');
    if (!token) {
      throw new Error('Authentication required');
    }

    const response = await axios.post(`${API_URL}/api/settings/licenses/`, licenseData, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
    return response.data;
  },

  /**
   * Update an existing license
   */
  async updateLicense(id: number, licenseData: Partial<License>): Promise<License> {
    const token = localStorage.getItem('token');
    if (!token) {
      throw new Error('Authentication required');
    }

    const response = await axios.patch(`${API_URL}/api/settings/licenses/${id}/`, licenseData, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
    return response.data;
  },

  /**
   * Get available packages
   */
  async getPackages(): Promise<Package[]> {
    const token = localStorage.getItem('token');
    if (!token) {
      throw new Error('Authentication required');
    }

    const response = await axios.get(`${API_URL}/api/settings/licenses/packages/`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
    return response.data;
  },

  /**
   * Get available modules
   */
  async getModules(): Promise<Module[]> {
    const token = localStorage.getItem('token');
    if (!token) {
      throw new Error('Authentication required');
    }

    const response = await axios.get(`${API_URL}/api/settings/licenses/modules/`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
    return response.data;
  }
};

export default licenseService;
