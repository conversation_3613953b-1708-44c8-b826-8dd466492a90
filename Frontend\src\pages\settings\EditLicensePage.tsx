import React, { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import PageBreadcrumb from '../../components/common/PageBreadCrumb';
import PageMeta from '../../components/common/PageMeta';
import LicenseForm from '../../components/settings/LicenseForm';
import { useAuth } from '../../context/AuthContext';

const EditLicensePage: React.FC = () => {
  const { user } = useAuth();
  const navigate = useNavigate();

  useEffect(() => {
    // Redirect non-superusers
    if (user && !user.is_superuser) {
      navigate('/settings/license/overview');
    }
  }, [user, navigate]);

  return (
    <>
      <PageMeta
        title="Edit License | ShuleXcel"
        description="Edit an existing school license."
      />
      <PageBreadcrumb pageTitle="Edit License" />

      <div className="container mx-auto px-4 py-6">
        <LicenseForm isEditMode={true} />
      </div>
    </>
  );
};

export default EditLicensePage;
