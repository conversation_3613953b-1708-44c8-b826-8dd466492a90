import React, { useState, useEffect } from 'react';
import licenseService, { License, Package, Module } from '../../services/licenseService';
import statisticsService from '../../services/statisticsService';
import LicenseAnalytics from './LicenseAnalytics';

interface LicenseManagerProps {
  isSuperUser: boolean;
}

const LicenseManager: React.FC<LicenseManagerProps> = ({ isSuperUser }) => {
  const [license, setLicense] = useState<License | null>(null);
  const [packages, setPackages] = useState<Package[]>([]);
  const [modules, setModules] = useState<Module[]>([]);
  const [studentCount, setStudentCount] = useState<number>(0);
  const [staffCount, setStaffCount] = useState<number>(0);
  const [branchCount, setBranchCount] = useState<number>(0);
  const [isLoading, setIsLoading] = useState(true);
  const [isStatsLoading, setIsStatsLoading] = useState(true);
  const [statsError, setStatsError] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchData = async () => {
      try {
        setIsLoading(true);
        setError(null);

        // Fetch license information
        const licenseData = await licenseService.getMyLicense();
        setLicense(licenseData);

        // Fetch packages and modules
        const [packagesData, modulesData] = await Promise.all([
          licenseService.getPackages(),
          licenseService.getModules()
        ]);

        setPackages(packagesData);
        setModules(modulesData);

        // Fetch statistics separately to handle potential errors
        setIsStatsLoading(true);
        try {
          const statistics = await statisticsService.getSchoolStatistics();
          setStudentCount(statistics.student_count);
          setStaffCount(statistics.staff_count);
          setBranchCount(statistics.branch_count);
          setStatsError(false);
        } catch (statsError) {
          console.error('Could not load statistics:', statsError);
          // Initialize with zeros instead of mock data
          setStudentCount(0);
          setStaffCount(0);
          setBranchCount(0);
          setStatsError(true);
        } finally {
          setIsStatsLoading(false);
        }
      } catch (err) {
        console.error('Error fetching license data:', err);
        setError('Failed to load license information. Please try again later.');
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, []);

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">
        <p>{error}</p>
      </div>
    );
  }

  if (!license) {
    return (
      <div className="bg-yellow-50 border border-yellow-200 text-yellow-700 px-4 py-3 rounded">
        <p>No license found for your school. Please contact your administrator.</p>
      </div>
    );
  }

  // Filter modules to show only those enabled in the license
  const enabledModules = modules.filter(module =>
    (license.enabled_modules && license.enabled_modules.includes(module.code)) || module.is_core
  );

  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="bg-white shadow rounded-lg p-6">
          <h2 className="text-xl font-semibold text-gray-800 mb-4">License Information</h2>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <p className="text-sm font-medium text-gray-500">License Key</p>
              <p className="text-base font-semibold">{license.formatted_license_key}</p>
            </div>

            <div>
              <p className="text-sm font-medium text-gray-500">Package</p>
              <p className="text-base font-semibold">{license.package_name}</p>
            </div>

            <div>
              <p className="text-sm font-medium text-gray-500">Status</p>
              <div className="flex items-center">
                <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                  license.is_active
                    ? 'bg-green-100 text-green-800'
                    : 'bg-red-100 text-red-800'
                }`}>
                  {license.subscription_status}
                </span>
                {license.is_active && (
                  <span className="ml-2 text-sm text-gray-500">
                    (Valid until {new Date(license.expiry_date).toLocaleDateString()})
                  </span>
                )}
              </div>
            </div>

            <div>
              <p className="text-sm font-medium text-gray-500">Created</p>
              <p className="text-base">
                {new Date(license.created_at).toLocaleDateString()}
              </p>
            </div>
          </div>
        </div>

        <LicenseAnalytics
          license={license}
          studentCount={studentCount}
          staffCount={staffCount}
          branchCount={branchCount}
          isStatsLoading={isStatsLoading}
          statsError={statsError}
        />
      </div>

      <div className="bg-white shadow rounded-lg p-6">
        <h2 className="text-xl font-semibold text-gray-800 mb-4">License Limits</h2>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="bg-blue-50 p-4 rounded-lg">
            <p className="text-sm font-medium text-gray-500">Maximum Students</p>
            <p className="text-2xl font-bold text-blue-600">{license.max_students}</p>
          </div>

          <div className="bg-green-50 p-4 rounded-lg">
            <p className="text-sm font-medium text-gray-500">Maximum Staff</p>
            <p className="text-2xl font-bold text-green-600">{license.max_staff}</p>
          </div>

          <div className="bg-purple-50 p-4 rounded-lg">
            <p className="text-sm font-medium text-gray-500">Maximum Branches</p>
            <p className="text-2xl font-bold text-purple-600">{license.max_branches}</p>
          </div>
        </div>
      </div>

      <div className="bg-white shadow rounded-lg p-6">
        <h2 className="text-xl font-semibold text-gray-800 mb-4">Enabled Modules</h2>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {enabledModules.map(module => (
            <div key={module.code} className="border border-gray-200 rounded-lg p-4">
              <div className="flex justify-between items-start">
                <h3 className="text-lg font-medium text-gray-800">{module.name}</h3>
                {module.is_core && (
                  <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                    Core
                  </span>
                )}
              </div>
              <p className="text-sm text-gray-500 mt-1">{module.description}</p>
            </div>
          ))}
        </div>
      </div>

      <div className="mt-6 flex justify-end space-x-4">
        <button
          type="button"
          className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          onClick={() => window.location.href = '/settings/licenses/usage'}
        >
          View Usage Dashboard
        </button>
        {isSuperUser && (
          <button
            type="button"
            className="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
            onClick={() => window.location.href = '/settings/licenses/manage'}
          >
            Manage Licenses
          </button>
        )}
      </div>
    </div>
  );
};

export default LicenseManager;
