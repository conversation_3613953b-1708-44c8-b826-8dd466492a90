import React, { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import licenseService, { License, Package } from '../../services/licenseService';
import schoolService from '../../services/schoolService';

interface School {
  id: number;
  name: string;
}

interface LicenseFormProps {
  isEditMode?: boolean;
}

const LicenseForm: React.FC<LicenseFormProps> = ({ isEditMode = false }) => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();

  // Get URL parameters for pre-populating form when redirected from school creation
  const urlParams = new URLSearchParams(window.location.search);
  const schoolIdFromUrl = urlParams.get('school_id');
  const schoolNameFromUrl = urlParams.get('school_name');
  
  const [formData, setFormData] = useState<Partial<License>>({
    school: schoolIdFromUrl ? parseInt(schoolIdFromUrl) : 0,
    package_type: 'basic',
    subscription_status: 'ACTIVE',
    start_date: new Date().toISOString().split('T')[0],
    expiry_date: new Date(new Date().setFullYear(new Date().getFullYear() + 1)).toISOString().split('T')[0],
    max_students: 500,
    max_staff: 50,
    max_branches: 1,
    custom_modules: []
  });
  
  const [schools, setSchools] = useState<School[]>([]);
  const [packages, setPackages] = useState<Package[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);

  useEffect(() => {
    const fetchData = async () => {
      try {
        setIsLoading(true);
        setError(null);

        // If in edit mode, validate the ID first
        if (isEditMode) {
          if (!id) {
            setError('No license ID provided for editing.');
            setIsLoading(false);
            return;
          }

          const licenseId = parseInt(id);
          if (isNaN(licenseId)) {
            setError('Invalid license ID provided.');
            setIsLoading(false);
            return;
          }
        }

        // Fetch schools and packages
        const [schoolsData, packagesData] = await Promise.all([
          schoolService.getAllSchools(),
          licenseService.getPackages()
        ]);

        setSchools(schoolsData);
        setPackages(packagesData);

        // If in edit mode, fetch the license data
        if (isEditMode && id) {
          const licenseId = parseInt(id); // We already validated this above
          const licenseData = await licenseService.getLicenseById(licenseId);
          setFormData({
            ...licenseData,
            start_date: new Date(licenseData.start_date).toISOString().split('T')[0],
            expiry_date: new Date(licenseData.expiry_date).toISOString().split('T')[0]
          });
        }
      } catch (err) {
        console.error('Error fetching data:', err);
        setError('Failed to load required data. Please try again later.');
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, [id, isEditMode]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;
    
    if (type === 'number') {
      setFormData(prev => ({ ...prev, [name]: parseInt(value) }));
    } else {
      setFormData(prev => ({ ...prev, [name]: value }));
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    try {
      setIsSaving(true);
      setError(null);
      setSuccessMessage(null);
      
      if (isEditMode && id) {
        // Validate that id is a valid number
        const licenseId = parseInt(id);
        if (isNaN(licenseId)) {
          setError('Invalid license ID provided.');
          return;
        }

        // Update existing license
        await licenseService.updateLicense(licenseId, formData);
        setSuccessMessage('License updated successfully!');
      } else {
        // Create new license
        await licenseService.createLicense(formData);
        setSuccessMessage('License created successfully!');

        // If redirected from school creation, go back to school management
        if (schoolIdFromUrl) {
          setTimeout(() => {
            navigate('/admin/schools');
          }, 2000);
        } else {
          // Reset form after successful creation for normal flow
          setFormData({
            school: 0,
            package_type: 'basic',
            subscription_status: 'ACTIVE',
            start_date: new Date().toISOString().split('T')[0],
            expiry_date: new Date(new Date().setFullYear(new Date().getFullYear() + 1)).toISOString().split('T')[0],
            max_students: 500,
            max_staff: 50,
            max_branches: 1,
            custom_modules: []
          });
        }
      }
    } catch (err) {
      console.error('Error saving license:', err);
      setError('Failed to save license. Please try again.');
    } finally {
      setIsSaving(false);
    }
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className="bg-white shadow rounded-lg p-6">
      <h2 className="text-xl font-semibold text-gray-800 mb-6">
        {isEditMode ? 'Edit License' : 'Create New License'}
      </h2>

      {schoolNameFromUrl && (
        <div className="bg-blue-50 border border-blue-200 text-blue-700 px-4 py-3 rounded mb-4">
          <p>
            <strong>Creating license for:</strong> {decodeURIComponent(schoolNameFromUrl)}
          </p>
          <p className="text-sm mt-1">
            The school has been successfully created. Please configure the license settings below.
          </p>
        </div>
      )}

      {error && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-4">
          <p>{error}</p>
        </div>
      )}

      {successMessage && (
        <div className="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded mb-4">
          <p>{successMessage}</p>
        </div>
      )}
      
      <form onSubmit={handleSubmit} className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label htmlFor="school" className="block text-sm font-medium text-gray-700 mb-1">
              School
            </label>
            <select
              id="school"
              name="school"
              value={formData.school || ''}
              onChange={handleChange}
              disabled={isEditMode}
              className="mt-1 block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
              required
            >
              <option value="">Select a school</option>
              {schools.map(school => (
                <option key={school.id} value={school.id}>
                  {school.name}
                </option>
              ))}
            </select>
          </div>
          
          <div>
            <label htmlFor="package_type" className="block text-sm font-medium text-gray-700 mb-1">
              Package Type
            </label>
            <select
              id="package_type"
              name="package_type"
              value={formData.package_type || 'basic'}
              onChange={handleChange}
              className="mt-1 block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
              required
            >
              {packages.map(pkg => (
                <option key={pkg.code} value={pkg.code}>
                  {pkg.name}
                </option>
              ))}
            </select>
          </div>
          
          <div>
            <label htmlFor="subscription_status" className="block text-sm font-medium text-gray-700 mb-1">
              Subscription Status
            </label>
            <select
              id="subscription_status"
              name="subscription_status"
              value={formData.subscription_status || 'ACTIVE'}
              onChange={handleChange}
              className="mt-1 block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
              required
            >
              <option value="ACTIVE">Active</option>
              <option value="TRIAL">Trial</option>
              <option value="EXPIRED">Expired</option>
              <option value="CANCELLED">Cancelled</option>
            </select>
          </div>
          
          <div>
            <label htmlFor="start_date" className="block text-sm font-medium text-gray-700 mb-1">
              Start Date
            </label>
            <input
              type="date"
              id="start_date"
              name="start_date"
              value={formData.start_date || ''}
              onChange={handleChange}
              className="mt-1 block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
              required
            />
          </div>
          
          <div>
            <label htmlFor="expiry_date" className="block text-sm font-medium text-gray-700 mb-1">
              Expiry Date
            </label>
            <input
              type="date"
              id="expiry_date"
              name="expiry_date"
              value={formData.expiry_date || ''}
              onChange={handleChange}
              className="mt-1 block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
              required
            />
          </div>
          
          <div>
            <label htmlFor="max_students" className="block text-sm font-medium text-gray-700 mb-1">
              Maximum Students
            </label>
            <input
              type="number"
              id="max_students"
              name="max_students"
              value={formData.max_students || 500}
              onChange={handleChange}
              min="1"
              className="mt-1 block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
              required
            />
          </div>
          
          <div>
            <label htmlFor="max_staff" className="block text-sm font-medium text-gray-700 mb-1">
              Maximum Staff
            </label>
            <input
              type="number"
              id="max_staff"
              name="max_staff"
              value={formData.max_staff || 50}
              onChange={handleChange}
              min="1"
              className="mt-1 block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
              required
            />
          </div>
          
          <div>
            <label htmlFor="max_branches" className="block text-sm font-medium text-gray-700 mb-1">
              Maximum Branches
            </label>
            <input
              type="number"
              id="max_branches"
              name="max_branches"
              value={formData.max_branches || 1}
              onChange={handleChange}
              min="1"
              className="mt-1 block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
              required
            />
          </div>
        </div>
        
        <div className="flex justify-end space-x-3">
          <button
            type="button"
            onClick={() => navigate('/settings/license/manage')}
            className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
          >
            Cancel
          </button>
          <button
            type="submit"
            disabled={isSaving}
            className="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50"
          >
            {isSaving ? 'Saving...' : isEditMode ? 'Update License' : 'Create License'}
          </button>
        </div>
      </form>
    </div>
  );
};

export default LicenseForm;
