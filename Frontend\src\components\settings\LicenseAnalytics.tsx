import React from 'react';
import { License } from '../../services/licenseService';

interface LicenseAnalyticsProps {
  license: License;
  studentCount?: number;
  staffCount?: number;
  branchCount?: number;
  isStatsLoading?: boolean;
  statsError?: boolean;
}

const LicenseAnalytics: React.FC<LicenseAnalyticsProps> = ({
  license,
  studentCount = 0,
  staffCount = 0,
  branchCount = 0,
  isStatsLoading = false,
  statsError = false
}) => {
  // Calculate days until expiry with proper validation
  const today = new Date();
  const expiryDate = new Date(license.expiry_date);

  // Validate dates and calculate days until expiry
  let daysUntilExpiry = 0;
  if (license.expiry_date && !isNaN(expiryDate.getTime())) {
    daysUntilExpiry = Math.ceil((expiryDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));
  }

  // Calculate usage percentages with safety checks
  const maxStudents = license.max_students || 1;
  const maxStaff = license.max_staff || 1;
  const maxBranches = license.max_branches || 1;

  const studentUsagePercent = Math.min(100, Math.round((studentCount / maxStudents) * 100));
  const staffUsagePercent = Math.min(100, Math.round((staffCount / maxStaff) * 100));
  const branchUsagePercent = Math.min(100, Math.round((branchCount / maxBranches) * 100));

  // Determine warning levels
  const isExpiryWarning = daysUntilExpiry <= 30;
  const isExpiryDanger = daysUntilExpiry <= 7;
  const isExpired = daysUntilExpiry <= 0;

  const isStudentWarning = studentUsagePercent >= 80;
  const isStudentDanger = studentUsagePercent >= 95;

  const isStaffWarning = staffUsagePercent >= 80;
  const isStaffDanger = staffUsagePercent >= 95;

  const isBranchWarning = branchUsagePercent >= 80;
  const isBranchDanger = branchUsagePercent >= 95;

  return (
    <div className="bg-white shadow rounded-lg p-6">
      <h2 className="text-xl font-semibold text-gray-800 mb-4">License Analytics</h2>

      {/* Expiry Status */}
      <div className={`mb-6 p-4 rounded-lg ${
        isExpired ? 'bg-red-50 border border-red-200' :
        isExpiryDanger ? 'bg-orange-50 border border-orange-200' :
        isExpiryWarning ? 'bg-yellow-50 border border-yellow-200' :
        'bg-green-50 border border-green-200'
      }`}>
        <div className="flex items-center justify-between">
          <div>
            <h3 className={`font-medium ${
              isExpired ? 'text-red-800' :
              isExpiryDanger ? 'text-orange-800' :
              isExpiryWarning ? 'text-yellow-800' :
              'text-green-800'
            }`}>
              {isExpired ? 'License Expired!' :
               isExpiryDanger ? 'License Expiring Soon!' :
               isExpiryWarning ? 'License Expiry Warning' :
               'License Status'}
            </h3>
            <p className="text-sm mt-1">
              {isExpired ? 'Your license has expired. Please renew it to continue using all features.' :
               license.expiry_date && !isNaN(expiryDate.getTime()) ?
               `Expires in ${daysUntilExpiry} days (${expiryDate.toLocaleDateString()})` :
               'Expiry date not available'}
            </p>
          </div>
          {!isExpired && license.expiry_date && !isNaN(expiryDate.getTime()) && (
            <div className="text-3xl font-bold">
              {daysUntilExpiry}
              <span className="text-sm font-normal ml-1">days</span>
            </div>
          )}
        </div>
      </div>

      {/* Usage Meters */}
      {isStatsLoading ? (
        <div className="flex justify-center items-center py-6">
          <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary"></div>
        </div>
      ) : statsError ? (
        <div className="bg-yellow-50 border border-yellow-200 text-yellow-700 px-4 py-3 rounded mb-4">
          <p>Could not load usage statistics. Using default values.</p>
        </div>
      ) : (
        <div className="space-y-6">
        <div>
          <div className="flex justify-between items-center mb-1">
            <h3 className="font-medium text-gray-700">Student Usage</h3>
            <span className={`text-sm ${
              isStudentDanger ? 'text-red-600' :
              isStudentWarning ? 'text-yellow-600' :
              'text-gray-600'
            }`}>
              {studentCount} / {license.max_students} ({studentUsagePercent}%)
            </span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2.5">
            <div
              className={`h-2.5 rounded-full ${
                isStudentDanger ? 'bg-red-600' :
                isStudentWarning ? 'bg-yellow-500' :
                'bg-green-600'
              }`}
              style={{ width: `${studentUsagePercent}%` }}
            ></div>
          </div>
          {isStudentDanger && (
            <p className="text-xs text-red-600 mt-1">
              You are approaching your student limit. Consider upgrading your license.
            </p>
          )}
        </div>

        <div>
          <div className="flex justify-between items-center mb-1">
            <h3 className="font-medium text-gray-700">Staff Usage</h3>
            <span className={`text-sm ${
              isStaffDanger ? 'text-red-600' :
              isStaffWarning ? 'text-yellow-600' :
              'text-gray-600'
            }`}>
              {staffCount} / {license.max_staff} ({staffUsagePercent}%)
            </span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2.5">
            <div
              className={`h-2.5 rounded-full ${
                isStaffDanger ? 'bg-red-600' :
                isStaffWarning ? 'bg-yellow-500' :
                'bg-green-600'
              }`}
              style={{ width: `${staffUsagePercent}%` }}
            ></div>
          </div>
          {isStaffDanger && (
            <p className="text-xs text-red-600 mt-1">
              You are approaching your staff limit. Consider upgrading your license.
            </p>
          )}
        </div>

        <div>
          <div className="flex justify-between items-center mb-1">
            <h3 className="font-medium text-gray-700">Branch Usage</h3>
            <span className={`text-sm ${
              isBranchDanger ? 'text-red-600' :
              isBranchWarning ? 'text-yellow-600' :
              'text-gray-600'
            }`}>
              {branchCount} / {license.max_branches} ({branchUsagePercent}%)
            </span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2.5">
            <div
              className={`h-2.5 rounded-full ${
                isBranchDanger ? 'bg-red-600' :
                isBranchWarning ? 'bg-yellow-500' :
                'bg-green-600'
              }`}
              style={{ width: `${branchUsagePercent}%` }}
            ></div>
          </div>
          {isBranchDanger && (
            <p className="text-xs text-red-600 mt-1">
              You are approaching your branch limit. Consider upgrading your license.
            </p>
          )}
        </div>
      </div>
      )}
    </div>
  );
};

export default LicenseAnalytics;
