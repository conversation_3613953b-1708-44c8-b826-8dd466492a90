from rest_framework import viewsets, status, permissions
from rest_framework.decorators import action
from rest_framework.response import Response
from django.shortcuts import get_object_or_404

from .license_models import LicenseSubscription, ModuleActivation, LicenseHistory
from .serializers import (
    LicenseSubscriptionSerializer, ModuleActivationSerializer,
    ModuleToggleSerializer, ModuleStatusSerializer, LicenseHistorySerializer
)
from .modules import AVAILABLE_MODULES, PACKAGE_TIERS, MODULE_STATUS, update_module_status, get_module_status
from schools.models import School, SchoolBranch
from core.permissions import IsSuperUserOrSchoolAdmin

class LicenseSubscriptionViewSet(viewsets.ModelViewSet):
    """
    API endpoint for managing license subscriptions
    """
    queryset = LicenseSubscription.objects.all()
    serializer_class = LicenseSubscriptionSerializer
    permission_classes = [permissions.IsAuthenticated, IsSuperUserOrSchoolAdmin]

    def get_queryset(self):
        user = self.request.user
        if user.is_superuser:
            return self.queryset

        # School admins can only see their own school's license
        if hasattr(user, 'school_branch') and user.school_branch:
            return self.queryset.filter(school=user.school_branch.school)

        return self.queryset.none()

    @action(detail=False, methods=['get'])
    def my_license(self, request):
        """Get the license for the current user's school"""
        user = request.user
        if not hasattr(user, 'school_branch') or not user.school_branch:
            return Response(
                {"error": "You are not associated with any school branch"},
                status=status.HTTP_404_NOT_FOUND
            )

        try:
            license_sub = LicenseSubscription.objects.get(school=user.school_branch.school)
            serializer = self.get_serializer(license_sub)
            return Response(serializer.data)
        except LicenseSubscription.DoesNotExist:
            return Response(
                {"error": "No license found for your school"},
                status=status.HTTP_404_NOT_FOUND
            )

    @action(detail=False, methods=['get'])
    def packages(self, request):
        """Get available package tiers"""
        packages = []
        for code, info in PACKAGE_TIERS.items():
            packages.append({
                'code': code,
                'name': info.get('name', code),
                'description': info.get('description', ''),
                'modules': info.get('modules', [])
            })

        return Response(packages)

    @action(detail=False, methods=['get'])
    def modules(self, request):
        """Get available modules"""
        modules = []
        for code, info in AVAILABLE_MODULES.items():
            modules.append({
                'code': code,
                'name': info.get('name', code),
                'description': info.get('description', ''),
                'is_core': info.get('is_core', False),
                'dependencies': info.get('dependencies', []),
                'app_names': info.get('app_names', []),
                'status': info.get('status', MODULE_STATUS['DEVELOPMENT'])
            })

        return Response(modules)

    @action(detail=False, methods=['post'])
    def update_module_status(self, request):
        """Update the development status of a module"""
        # Only superusers can update module status
        if not request.user.is_superuser:
            return Response(
                {"error": "Only superusers can update module status"},
                status=status.HTTP_403_FORBIDDEN
            )

        serializer = ModuleStatusSerializer(data=request.data)
        if not serializer.is_valid():
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        module_code = serializer.validated_data['module_code']
        new_status = serializer.validated_data['status']

        # Check if module exists
        if module_code not in AVAILABLE_MODULES:
            return Response(
                {"error": f"Module '{module_code}' does not exist"},
                status=status.HTTP_404_NOT_FOUND
            )

        # Update module status
        if update_module_status(module_code, new_status):
            # Return updated module info
            module_info = AVAILABLE_MODULES[module_code].copy()
            module_info['code'] = module_code
            return Response(module_info)
        else:
            return Response(
                {"error": f"Failed to update module status"},
                status=status.HTTP_400_BAD_REQUEST
            )

class ModuleActivationViewSet(viewsets.ModelViewSet):
    """
    API endpoint for managing module activations
    """
    queryset = ModuleActivation.objects.all()
    serializer_class = ModuleActivationSerializer
    permission_classes = [permissions.IsAuthenticated, IsSuperUserOrSchoolAdmin]

    def get_queryset(self):
        user = self.request.user
        if user.is_superuser:
            return self.queryset

        # School admins can only see their own school's module activations
        if hasattr(user, 'school_branch') and user.school_branch:
            return self.queryset.filter(school_branch__school=user.school_branch.school)

        return self.queryset.none()

    @action(detail=False, methods=['get'])
    def my_modules(self, request):
        """Get the module activation for the current user's school branch"""
        user = request.user
        if not hasattr(user, 'school_branch') or not user.school_branch:
            return Response(
                {"error": "You are not associated with any school branch"},
                status=status.HTTP_404_NOT_FOUND
            )

        try:
            module_activation = ModuleActivation.objects.get(school_branch=user.school_branch)
            serializer = self.get_serializer(module_activation, context={'request': request})
            return Response(serializer.data)
        except ModuleActivation.DoesNotExist:
            return Response(
                {"error": "No module activation found for your school branch"},
                status=status.HTTP_404_NOT_FOUND
            )

    @action(detail=True, methods=['post'])
    def toggle_module(self, request, pk=None):
        """Enable or disable a module"""
        module_activation = self.get_object()
        serializer = ModuleToggleSerializer(data=request.data)

        if not serializer.is_valid():
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        module_code = serializer.validated_data['module_code']
        enable = serializer.validated_data['enable']

        # Check if module exists
        if module_code not in AVAILABLE_MODULES:
            return Response(
                {"error": f"Module '{module_code}' does not exist"},
                status=status.HTTP_404_NOT_FOUND
            )

        # Check if module is core (cannot be disabled)
        if not enable and AVAILABLE_MODULES[module_code].get('is_core', False):
            return Response(
                {"error": f"Module '{module_code}' is a core module and cannot be disabled"},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Check if module is available in the school's license
        try:
            license_sub = module_activation.school_branch.school.license
            if not license_sub.is_module_enabled(module_code):
                return Response(
                    {"error": f"Module '{module_code}' is not available in your license"},
                    status=status.HTTP_403_FORBIDDEN
                )
        except:
            # If no license exists, only allow core modules
            if not AVAILABLE_MODULES[module_code].get('is_core', False):
                return Response(
                    {"error": f"Module '{module_code}' is not available in your license"},
                    status=status.HTTP_403_FORBIDDEN
                )

        # Enable or disable the module
        if enable:
            if module_code not in module_activation.enabled_modules:
                module_activation.enabled_modules.append(module_code)
                module_activation.save()
        else:
            if module_code in module_activation.enabled_modules:
                module_activation.enabled_modules.remove(module_code)
                module_activation.save()

        serializer = self.get_serializer(module_activation, context={'request': request})
        return Response(serializer.data)


class LicenseHistoryViewSet(viewsets.ReadOnlyModelViewSet):
    """
    API endpoint for viewing license history
    Read-only viewset - history entries can only be created through the model's save method
    """
    queryset = LicenseHistory.objects.all().order_by('-timestamp')
    serializer_class = LicenseHistorySerializer
    permission_classes = [permissions.IsAuthenticated, IsSuperUserOrSchoolAdmin]

    def get_queryset(self):
        user = self.request.user
        if user.is_superuser:
            return self.queryset

        # School admins can only see their own school's license history
        if hasattr(user, 'school_branch') and user.school_branch:
            return self.queryset.filter(license__school=user.school_branch.school)

        return self.queryset.none()

    @action(detail=False, methods=['get'])
    def my_license_history(self, request):
        """Get the history for the current user's school license"""
        user = request.user
        if not hasattr(user, 'school_branch') or not user.school_branch:
            return Response(
                {"error": "You are not associated with any school branch"},
                status=status.HTTP_404_NOT_FOUND
            )

        try:
            # Get the license for the user's school
            license_obj = LicenseSubscription.objects.get(school=user.school_branch.school)
            # Get the history for that license
            history = self.queryset.filter(license=license_obj)
            page = self.paginate_queryset(history)
            if page is not None:
                serializer = self.get_serializer(page, many=True)
                return self.get_paginated_response(serializer.data)

            serializer = self.get_serializer(history, many=True)
            return Response(serializer.data)
        except LicenseSubscription.DoesNotExist:
            return Response(
                {"error": "No license found for your school"},
                status=status.HTTP_404_NOT_FOUND
            )
