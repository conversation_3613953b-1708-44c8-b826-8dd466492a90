from rest_framework import serializers

class SchoolStatisticsSerializer(serializers.Serializer):
    """Serializer for school statistics"""
    student_count = serializers.IntegerField()
    staff_count = serializers.IntegerField()
    branch_count = serializers.IntegerField()
    active_users_count = serializers.IntegerField()
    total_users_count = serializers.IntegerField()
    student_users = serializers.IntegerField(required=False, default=0)
    teacher_users = serializers.IntegerField(required=False, default=0)
    staff_users = serializers.IntegerField(required=False, default=0)
    active_last_month = serializers.IntegerField(required=False, default=0)
