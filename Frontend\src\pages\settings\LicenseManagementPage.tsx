import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import PageBreadcrumb from '../../components/common/PageBreadCrumb';
import PageMeta from '../../components/common/PageMeta';
import licenseService, { License } from '../../services/licenseService';
import schoolService from '../../services/schoolService';
import { useAuth } from '../../context/AuthContext';
import LicenseHistoryTab from '../../components/settings/LicenseHistoryTab';

const LicenseManagementPage: React.FC = () => {
  const [licenses, setLicenses] = useState<License[]>([]);
  const [filteredLicenses, setFilteredLicenses] = useState<License[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isLoadingSchools, setIsLoadingSchools] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Filter state
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('asc');

  // Tab state
  const [activeTab, setActiveTab] = useState<'licenses' | 'history'>('licenses');
  const [selectedLicense, setSelectedLicense] = useState<License | null>(null);

  const { user } = useAuth();
  const navigate = useNavigate();



  // Function to apply filters and sorting
  const applyFilters = (licenses: License[], search: string, status: string, sort: 'asc' | 'desc') => {
    let result = [...licenses];

    // Apply search filter
    if (search.trim() !== '') {
      const searchLower = search.toLowerCase();
      result = result.filter(license =>
        (license.school_name?.toLowerCase().includes(searchLower) ||
         license.formatted_license_key.toLowerCase().includes(searchLower))
      );
    }

    // Apply status filter
    if (status !== 'all') {
      result = result.filter(license => {
        if (status === 'active') return license.is_active;
        if (status === 'expired') return !license.is_active;
        if (status === 'trial') return license.package_type === 'trial';
        return true;
      });
    }

    // Apply sorting by expiry date
    result.sort((a, b) => {
      const dateA = new Date(a.expiry_date).getTime();
      const dateB = new Date(b.expiry_date).getTime();
      return sort === 'asc' ? dateA - dateB : dateB - dateA;
    });

    setFilteredLicenses(result);
  };

  // Effect to reapply filters when filter criteria change
  useEffect(() => {
    if (licenses.length > 0) {
      applyFilters(licenses, searchTerm, statusFilter, sortOrder);
    }
  }, [licenses, searchTerm, statusFilter, sortOrder]);

  useEffect(() => {
    // Redirect non-superusers
    if (user && !user.is_superuser) {
      navigate('/settings/license/overview');
      return;
    }

    // Define fetchLicenses function inside useEffect
    const fetchLicenses = async (schoolId?: number) => {
      try {
        setIsLoading(true);
        // If schoolId is provided, filter licenses by school
        const data = schoolId
          ? await licenseService.getAllLicenses(schoolId)
          : await licenseService.getAllLicenses();

        // Ensure data is an array
        if (Array.isArray(data)) {
          // First set licenses with just IDs so the user sees something quickly
          setLicenses(data);
          setIsLoading(false);

          // Then fetch school information for each license
          setIsLoadingSchools(true);
          try {
            const licensesWithSchoolNames = await Promise.all(
              data.map(async (license) => {
                try {
                  const schoolData = await schoolService.getSchoolById(license.school);
                  return {
                    ...license,
                    school_name: schoolData.name
                  };
                } catch {
                  // Silently handle school fetch errors
                  return {
                    ...license,
                    school_name: `School ID: ${license.school}`
                  };
                }
              })
            );

            const updatedLicenses = licensesWithSchoolNames;
            setLicenses(updatedLicenses);
            // Apply initial filtering
            applyFilters(updatedLicenses, searchTerm, statusFilter, sortOrder);
          } catch {
            // Silently handle errors fetching school information
          } finally {
            setIsLoadingSchools(false);
          }
        } else {
          // Handle unexpected response format
          setLicenses([]);
          setError('Received unexpected data format from server.');
        }
      } catch {
        // Silently handle license fetch errors
        setLicenses([]);
        setError('Failed to load licenses. Please try again later.');
      } finally {
        setIsLoading(false);
      }
    };

    // Initial fetch of all licenses
    fetchLicenses();
  }, [user, navigate, searchTerm, statusFilter, sortOrder]);

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64 dark:bg-gray-900">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary dark:border-indigo-500"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded dark:bg-red-900/20 dark:border-red-800 dark:text-red-400">
        <p>{error}</p>
      </div>
    );
  }

  return (
    <>
      <PageMeta
        title="License Administration | ShuleXcel"
        description="Manage all licenses in the system."
      />
      <PageBreadcrumb pageTitle="License Administration" />

      <div className="container mx-auto px-4 py-6 dark:bg-gray-900">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">License Management</h1>
          <div className="flex space-x-3">
            <button
              type="button"
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:bg-blue-700 dark:hover:bg-blue-800 dark:focus:ring-blue-600"
              onClick={() => navigate('/settings/license/analytics')}
            >
              Usage Dashboard
            </button>
            <button
              type="button"
              className="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 dark:bg-indigo-700 dark:hover:bg-indigo-800 dark:focus:ring-indigo-600"
              onClick={() => navigate('/settings/license/create')}
            >
              Create New License
            </button>
          </div>
        </div>

        {/* Tabs */}
        <div className="mb-6">
          <div className="border-b border-gray-200 dark:border-gray-700">
            <nav className="-mb-px flex space-x-8">
              <button
                type="button"
                className={`${activeTab === 'licenses' ? 'border-indigo-500 text-indigo-600 dark:border-indigo-400 dark:text-indigo-400' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300 dark:hover:border-gray-600'} whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm`}
                onClick={() => setActiveTab('licenses')}
              >
                Active Licenses
              </button>
              <button
                type="button"
                className={`${activeTab === 'history' ? 'border-indigo-500 text-indigo-600 dark:border-indigo-400 dark:text-indigo-400' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300 dark:hover:border-gray-600'} whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm`}
                onClick={() => setActiveTab('history')}
              >
                License History
              </button>
            </nav>
          </div>
        </div>

        {isLoadingSchools && (
          <div className="mb-4 p-2 bg-blue-50 border border-blue-200 text-blue-700 rounded flex items-center dark:bg-blue-900/20 dark:border-blue-800 dark:text-blue-400">
            <div className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-blue-700 mr-2 dark:border-blue-400"></div>
            <span>Loading school information...</span>
          </div>
        )}

        {activeTab === 'licenses' && (
          <>
            {/* Search and Filter UI */}
            <div className="bg-white shadow rounded-lg p-4 mb-6 dark:bg-gray-800 dark:shadow-gray-900/30">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                {/* Search Input */}
                <div>
                  <label htmlFor="search" className="block text-sm font-medium text-gray-700 mb-1 dark:text-gray-300">
                    Search Licenses
                  </label>
                  <div className="relative">
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-gray-400 dark:text-gray-500" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clipRule="evenodd" />
                      </svg>
                    </div>
                    <input
                      type="text"
                      id="search"
                      className="pl-10 w-full rounded-md border border-gray-300 py-2 text-gray-900 shadow-sm focus:border-indigo-500 focus:outline-none focus:ring-1 focus:ring-indigo-500 dark:border-gray-700 dark:bg-gray-700 dark:text-white dark:focus:border-indigo-600 dark:focus:ring-indigo-600/50 dark:placeholder-gray-400"
                      placeholder="Search by school name or license key"
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                    />
                  </div>
                </div>

                {/* Status Filter */}
                <div>
                  <label htmlFor="status" className="block text-sm font-medium text-gray-700 mb-1 dark:text-gray-300">
                    License Status
                  </label>
                  <select
                    id="status"
                    className="w-full rounded-md border border-gray-300 py-2 pl-3 pr-10 text-gray-900 shadow-sm focus:border-indigo-500 focus:outline-none focus:ring-1 focus:ring-indigo-500 dark:border-gray-700 dark:bg-gray-700 dark:text-white dark:focus:border-indigo-600 dark:focus:ring-indigo-600/50"
                    value={statusFilter}
                    onChange={(e) => setStatusFilter(e.target.value)}
                  >
                    <option value="all">All Statuses</option>
                    <option value="active">Active</option>
                    <option value="expired">Expired</option>
                    <option value="trial">Trial</option>
                  </select>
                </div>

                {/* Sort Order */}
                <div>
                  <label htmlFor="sort" className="block text-sm font-medium text-gray-700 mb-1 dark:text-gray-300">
                    Sort by Expiry Date
                  </label>
                  <select
                    id="sort"
                    className="w-full rounded-md border border-gray-300 py-2 pl-3 pr-10 text-gray-900 shadow-sm focus:border-indigo-500 focus:outline-none focus:ring-1 focus:ring-indigo-500 dark:border-gray-700 dark:bg-gray-700 dark:text-white dark:focus:border-indigo-600 dark:focus:ring-indigo-600/50"
                    value={sortOrder}
                    onChange={(e) => setSortOrder(e.target.value as 'asc' | 'desc')}
                  >
                    <option value="asc">Oldest First</option>
                    <option value="desc">Newest First</option>
                  </select>
                </div>
              </div>
            </div>

            <div className="bg-white shadow overflow-hidden sm:rounded-md dark:bg-gray-800 dark:shadow-gray-900/30">
              <ul className="divide-y divide-gray-200 dark:divide-gray-700">
                {!filteredLicenses || filteredLicenses.length === 0 ? (
                  <li className="px-6 py-4 text-center text-gray-500 dark:text-gray-400">
                    {licenses.length > 0 ? 'No licenses match your search criteria' : 'No licenses found'}
                  </li>
                ) : (
                  filteredLicenses.map(license => (
                    <li key={license.id} className="px-6 py-4 hover:bg-gray-50 dark:hover:bg-gray-700/50">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-lg font-medium text-indigo-600 dark:text-indigo-400">
                            {license.school_name || `School ID: ${license.school}`}
                          </p>
                          <p className="text-sm text-gray-500 dark:text-gray-400">
                            License Key: {license.formatted_license_key}
                          </p>
                          <div className="mt-2 flex items-center">
                            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                              license.is_active
                                ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400'
                                : 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400'
                            }`}>
                              {license.subscription_status}
                            </span>
                            <span className="ml-2 text-sm text-gray-500 dark:text-gray-400">
                              {license.package_name} Package
                            </span>
                            <span className="ml-2 text-sm text-gray-500 dark:text-gray-400">
                              Expires: {new Date(license.expiry_date).toLocaleDateString()}
                            </span>
                          </div>
                        </div>
                        <div>
                          <div className="space-x-4">
                            <button
                              type="button"
                              className="text-indigo-600 hover:text-indigo-900 dark:text-indigo-400 dark:hover:text-indigo-300"
                              onClick={() => navigate(`/settings/license/edit/${license.id}`)}
                            >
                              Edit
                            </button>
                            <button
                              type="button"
                              className="text-green-600 hover:text-green-900 dark:text-green-400 dark:hover:text-green-300"
                              onClick={() => navigate(`/settings/license/renew/${license.id}`)}
                            >
                              Renew
                            </button>
                            <button
                              type="button"
                              className="text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300"
                              onClick={() => {
                                setSelectedLicense(license);
                                setActiveTab('history');
                              }}
                            >
                              View History
                            </button>
                          </div>
                        </div>
                      </div>
                    </li>
                  ))
                )}
              </ul>
            </div>
          </>
        )}

        {activeTab === 'history' && (
          <LicenseHistoryTab selectedLicense={selectedLicense} />
        )}
      </div>
    </>
  );
};

export default LicenseManagementPage;
