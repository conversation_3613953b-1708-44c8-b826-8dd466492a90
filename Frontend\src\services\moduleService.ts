import axios from 'axios';
import { API_URL } from '../config';

// Additional types for ModulesSettings component
export interface SchoolBranch {
  id: number;
  name: string;
  enabled_modules: string[];
  [key: string]: unknown;
}

export interface Module {
  code: string;
  name: string;
  description: string;
  is_core: boolean;
  status: string;
  dependencies: string[];
  app_names: string[];
  is_enabled?: boolean;
  is_visible?: boolean;
}

export interface ModuleActivation {
  id: number;
  school_branch: number;
  enabled_modules: string[];
  enabled_modules_details: Module[];
  available_modules: Module[];
  created_at: string;
  updated_at: string;
}

// Get all available modules
export const getAvailableModules = async (): Promise<Module[]> => {
  const token = localStorage.getItem('token');
  if (!token) {
    throw new Error('Authentication required');
  }

  const response = await axios.get(`${API_URL}/api/settings/licenses/modules/`, {
    headers: {
      'Authorization': `Bearer ${token}`
    }
  });
  return response.data;
};

// Get modules for the current user's branch
export const getMyModules = async (): Promise<ModuleActivation> => {
  const token = localStorage.getItem('token');
  if (!token) {
    throw new Error('Authentication required');
  }

  try {
    // First try to get the user's profile to get their school branch
    // Use the correct endpoint: /api/users/profile/ instead of /api/users/me/
    const userResponse = await axios.get(`${API_URL}/api/users/profile/`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });

    const user = userResponse.data;

    // Check if user has a school branch - handle different response formats
    let branchId = null;

    // Format 1: school_branch is an object with id property
    if (user.school_branch && typeof user.school_branch === 'object' && user.school_branch.id) {
      branchId = user.school_branch.id;
    }
    // Format 2: school_branch is a number (direct ID)
    else if (user.school_branch && typeof user.school_branch === 'number') {
      branchId = user.school_branch;
    }

    if (!branchId) {
      // Only show warning for non-super users (check if user exists and is not superuser)
      if (!user || !user.is_superuser) {
        // Only log warning if user exists but is not a superuser
        if (user && !user.is_superuser) {
          console.warn('User is not associated with any school branch, fetching modules without branch filter');
        }
      } else {
        console.log('Super user detected - fetching all available modules');
      }

      // Get available modules without branch filter
      const modulesResponse = await axios.get(`${API_URL}/api/settings/licenses/modules/`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      // For super users, enable all modules by default
      const enabledModules = user.is_superuser
        ? (modulesResponse.data || []).map((m: Module) => m.code)
        : [];

      // Return a default structure with available modules
      return {
        id: 0,
        school_branch: 0,
        enabled_modules: enabledModules,
        enabled_modules_details: [],
        available_modules: modulesResponse.data || [],
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };
    }



    // Branch ID is already logged above

    // Then get the modules for that branch
    let response;
    try {
      response = await axios.get(`${API_URL}/api/settings/modules/?school_branch=${branchId}`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
    } catch (branchError) {
      console.error('Error fetching modules for branch:', branchError);

      // Check if user is a superuser
      if (user.is_superuser)  {
        console.log('User is a superuser, fetching all available modules');

        // Get available modules for superusers
        const modulesResponse = await axios.get(`${API_URL}/api/settings/licenses/modules/`, {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        });

        // Return a structure with all modules enabled for superusers
        return {
          id: 0,
          school_branch: branchId,
          enabled_modules: modulesResponse.data.map((m: Module) => m.code) || [],
          enabled_modules_details: [],
          available_modules: modulesResponse.data || [],
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        };
      }

      // For non-superusers, get available modules without enabling them
      const modulesResponse = await axios.get(`${API_URL}/api/settings/licenses/modules/`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      return {
        id: 0,
        school_branch: branchId,
        enabled_modules: [],
        enabled_modules_details: [],
        available_modules: modulesResponse.data || [],
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };
    }

    // If there are no module activations for this branch, create a default one
    if (response.data.length === 0) {

      // Get available modules
      const modulesResponse = await axios.get(`${API_URL}/api/settings/licenses/modules/`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      // Return a default structure with available modules
      return {
        id: 0,
        school_branch: branchId, // Use the actual branch ID
        enabled_modules: [],
        enabled_modules_details: [],
        available_modules: modulesResponse.data || [],
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };
    }

    return response.data[0];
  } catch (error) {
    // Log more detailed information for debugging
    if (axios.isAxiosError(error)) {
      console.error('API Error details:', {
        status: error.response?.status,
        statusText: error.response?.statusText,
        data: error.response?.data,
        url: error.config?.url
      });
    }

    // If the endpoint doesn't exist, return a default structure
    // This is a temporary solution until the backend endpoint is fixed
    return {
      id: 0,
      school_branch: 0,
      enabled_modules: [],
      enabled_modules_details: [],
      available_modules: [],
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };
  }
};

// Update module status (development/live)
export const updateModuleStatus = async (moduleCode: string, status: string): Promise<Module> => {
  const token = localStorage.getItem('token');
  if (!token) {
    throw new Error('Authentication required');
  }

  const response = await axios.post(`${API_URL}/api/settings/licenses/update_module_status/`, {
    module_code: moduleCode,
    status: status
  }, {
    headers: {
      'Authorization': `Bearer ${token}`
    }
  });
  return response.data;
};

// Toggle module for a branch
export const toggleModuleForBranch = async (
  moduleActivationId: number,
  moduleCode: string,
  enable: boolean
): Promise<ModuleActivation> => {
  const token = localStorage.getItem('token');
  if (!token) {
    throw new Error('Authentication required');
  }

  try {
    // Log the request details for debugging
    console.log('Toggle module request:', {
      url: `${API_URL}/api/settings/modules/${moduleActivationId}/toggle_module/`,
      data: { module_code: moduleCode, enable: enable }
    });

    const response = await axios.post(`${API_URL}/api/settings/modules/${moduleActivationId}/toggle_module/`, {
      module_code: moduleCode,
      enable: enable
    }, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });

    // Log successful response
    console.log('Toggle module response:', response.data);

    // Verify the response format
    if (response.data && typeof response.data === 'object') {
      // If the response doesn't have enabled_modules, try to fetch the updated module activation
      if (!response.data.enabled_modules) {
        console.log('Response does not contain enabled_modules, fetching updated module activation');

        // Fetch the updated module activation
        const updatedResponse = await axios.get(`${API_URL}/api/settings/modules/${moduleActivationId}/`, {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        });

        console.log('Updated module activation:', updatedResponse.data);
        return updatedResponse.data;
      }
    }

    return response.data;
  } catch (error) {
    console.error(`Error in toggleModuleForBranch for activation ${moduleActivationId}, module ${moduleCode}:`, error);

    if (axios.isAxiosError(error)) {
      // Log detailed error information
      console.error('Status:', error.response?.status);
      console.error('Status text:', error.response?.statusText);
      console.error('URL:', error.config?.url);
      console.error('Request data:', error.config?.data);

      if (error.response?.data) {
        console.error('Response data:', JSON.stringify(error.response.data, null, 2));
      }

      // Special handling for 404 errors (module activation not found)
      if (error.response?.status === 404) {
        throw new Error(`Module activation with ID ${moduleActivationId} not found. It may have been deleted.`);
      }
    }

    throw error;
  }
};

// Check if a module is visible to the current user
export const isModuleVisible = (modules: ModuleActivation | null, moduleCode: string): boolean => {
  if (!modules) return false;

  // Check if the user is a superuser (based on enabled_modules containing all modules)
  const isSuperUser = modules.available_modules.length > 0 &&
                     modules.available_modules.every(m => modules.enabled_modules.includes(m.code));

  // Superusers can see all modules
  if (isSuperUser) return true;

  // Core modules are always visible
  if (moduleCode === 'core' || moduleCode === 'academics') return true;

  // Find the module in available_modules
  const module = modules.available_modules.find(m => m.code === moduleCode);

  // If module is not found or not visible, return false
  if (!module || !module.is_visible) return false;

  return true;
};

// Additional functions for ModulesSettings component
export const fetchModulesForSettings = async (): Promise<Module[]> => {
  const token = localStorage.getItem('token');
  if (!token) {
    throw new Error('Authentication required');
  }

  const response = await axios.get(`${API_URL}/api/settings/licenses/modules/`, {
    headers: {
      'Authorization': `Bearer ${token}`
    }
  });
  return response.data;
};

export const fetchBranches = async (schoolId: number): Promise<SchoolBranch[]> => {
  const token = localStorage.getItem('token');
  if (!token) {
    throw new Error('Authentication required');
  }

  const response = await axios.get(`${API_URL}/api/schools/branch/?school=${schoolId}`, {
    headers: {
      'Authorization': `Bearer ${token}`
    }
  });

  // Extract branches data from the paginated response
  return response.data.results || [];
};

export interface PaginatedResponse<T> {
  count: number;
  next: string | null;
  previous: string | null;
  results: T[];
}

export const fetchBranchModules = async (branchId: number): Promise<PaginatedResponse<ModuleActivation> | ModuleActivation[]> => {
  const token = localStorage.getItem('token');
  if (!token) {
    throw new Error('Authentication required');
  }

  try {
    // Log the request details for debugging
    console.log(`Fetching modules for branch ID: ${branchId}`);

    const response = await axios.get(`${API_URL}/api/settings/modules/?school_branch=${branchId}`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });

    // Log successful response
    console.log(`Modules for branch ${branchId}:`, response.data);

    // Return the data as is - it could be either a paginated response or a direct array
    return response.data;
  } catch (error) {
    console.error(`Error in fetchBranchModules for branch ${branchId}:`, error);

    if (axios.isAxiosError(error)) {
      // Log detailed error information
      console.error('Status:', error.response?.status);
      console.error('Status text:', error.response?.statusText);
      console.error('URL:', error.config?.url);

      if (error.response?.data) {
        console.error('Response data:', JSON.stringify(error.response.data, null, 2));
      }
    }

    throw error;
  }
};

export const fetchBranchesModulesBatch = async (branchIds: number[]): Promise<ModuleActivation[]> => {
  const token = localStorage.getItem('token');
  if (!token) {
    throw new Error('Authentication required');
  }

  try {
    const response = await axios.get(
      `${API_URL}/api/settings/modules/?school_branch__in=${branchIds.join(',')}`,
      {
        headers: { 'Authorization': `Bearer ${token}` }
      }
    );
    return response.data;
  } catch (error) {
    console.error('Batch module request failed:',
      axios.isAxiosError(error) ? error.message : 'Unknown error');
    return [];
  }
};

export const createModuleActivation = async (branchId: number): Promise<ModuleActivation> => {
  const token = localStorage.getItem('token');
  if (!token) {
    throw new Error('Authentication required');
  }

  try {
    // Log the request details for debugging
    console.log('Create module activation request:', {
      url: `${API_URL}/api/settings/modules/`,
      data: { school_branch: branchId, enabled_modules: [] }
    });

    const response = await axios.post(`${API_URL}/api/settings/modules/`, {
      school_branch: branchId,
      enabled_modules: []
    }, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });

    // Log successful response
    console.log('Create module activation response:', response.data);
    return response.data;
  } catch (error) {
    console.error(`Error in createModuleActivation for branch ${branchId}:`, error);

    if (axios.isAxiosError(error)) {
      // Log detailed error information
      console.error('Status:', error.response?.status);
      console.error('Status text:', error.response?.statusText);
      console.error('URL:', error.config?.url);
      console.error('Request data:', error.config?.data);

      if (error.response?.data) {
        console.error('Response data:', JSON.stringify(error.response.data, null, 2));
      }
    }

    throw error;
  }
};

export const toggleModuleForBranchById = async (
  branchId: number,
  moduleCode: string,
  enable: boolean
): Promise<ModuleActivation> => {
  const token = localStorage.getItem('token');
  if (!token) {
    throw new Error('Authentication required');
  }

  try {
    // Find the module activation for this branch
    console.log(`Fetching module activation for branch ID: ${branchId}`);
    const response = await fetchBranchModules(branchId);

    // Debug log
    console.log(`Module activation response:`, response);

    let moduleActivationId: number | null = null;

    // Check if we have a paginated response with results
    if (response && typeof response === 'object' && 'results' in response &&
        Array.isArray(response.results) && response.results.length > 0) {
      // Extract the first module activation from results
      const moduleActivation = response.results[0];
      if (moduleActivation && typeof moduleActivation === 'object' && 'id' in moduleActivation) {
        moduleActivationId = moduleActivation.id;
        console.log(`Found existing module activation ID in paginated results: ${moduleActivationId}`);
      }
    }
    // Check if we have a direct array response
    else if (response && Array.isArray(response) && response.length > 0) {
      const moduleActivation = response[0];
      if (moduleActivation && typeof moduleActivation === 'object' && 'id' in moduleActivation) {
        moduleActivationId = moduleActivation.id;
        console.log(`Found existing module activation ID in array: ${moduleActivationId}`);
      }
    }

    // If we found a module activation ID, toggle the module
    if (moduleActivationId) {
      console.log(`Toggling module ${moduleCode} (${enable ? 'enable' : 'disable'}) for activation ID: ${moduleActivationId}`);
      return await toggleModuleForBranch(moduleActivationId, moduleCode, enable);
    }

    // No existing module activation found, try to create one
    console.log(`No module activation found, creating new one for branch ID: ${branchId}`);

    try {
      const createResponse = await createModuleActivation(branchId);
      if (createResponse && typeof createResponse === 'object' && 'id' in createResponse) {
        moduleActivationId = createResponse.id;
        console.log(`Created new module activation with ID: ${moduleActivationId}`);

        // Toggle the module
        console.log(`Toggling module ${moduleCode} (${enable ? 'enable' : 'disable'}) for activation ID: ${moduleActivationId}`);
        return await toggleModuleForBranch(moduleActivationId, moduleCode, enable);
      } else {
        throw new Error(`Failed to create module activation: Invalid response format`);
      }
    }
    catch (createError) {
      console.error('Error creating module activation:', createError);

      // Check if the error is because the module activation already exists
      if (axios.isAxiosError(createError) && createError.response) {
        console.error('Create error details:', JSON.stringify(createError.response.data, null, 2));

        const errorData = createError.response.data;
        if (errorData &&
            errorData.school_branch &&
            Array.isArray(errorData.school_branch) &&
            errorData.school_branch.includes("module activation with this school branch already exists.")) {

          console.log("Module activation already exists, fetching existing ones...");

          // Try multiple approaches to find the existing module activation
          moduleActivationId = await findExistingModuleActivation(branchId);

          if (moduleActivationId) {
            console.log(`Found existing module activation ID: ${moduleActivationId}`);

            // Toggle the module
            console.log(`Toggling module ${moduleCode} (${enable ? 'enable' : 'disable'}) for activation ID: ${moduleActivationId}`);
            return await toggleModuleForBranch(moduleActivationId, moduleCode, enable);
          }

          // If we still can't find the module activation, throw a more helpful error
          throw new Error(`Could not find existing module activation for branch ${branchId} despite it existing`);
        }
      }

      // If it's not the specific error we're handling, rethrow
      throw createError;
    }
  } catch (error) {
    console.error(`Error in toggleModuleForBranchById for branch ${branchId}, module ${moduleCode}:`, error);
    if (axios.isAxiosError(error) && error.response) {
      console.error('Error details:', JSON.stringify(error.response.data, null, 2));
    }
    throw error;
  }
};

// Helper function to find an existing module activation using multiple approaches
async function findExistingModuleActivation(branchId: number): Promise<number | null> {
  const token = localStorage.getItem('token');
  if (!token) {
    throw new Error('Authentication required');
  }

  // Try multiple approaches to find the existing module activation

  // Approach 1: Try the direct API endpoint if available
  try {
    console.log(`Trying direct API endpoint for branch ${branchId}`);
    const directResponse = await axios.get(
      `${API_URL}/api/settings/modules/by-branch/${branchId}/`,
      { headers: { 'Authorization': `Bearer ${token}` } }
    );

    if (directResponse.data && typeof directResponse.data === 'object' && 'id' in directResponse.data) {
      return directResponse.data.id;
    }
  } catch (directError) {
    console.log("Direct API approach failed:", directError.message);
  }

  // Approach 2: Try the list endpoint with a filter
  try {
    console.log(`Trying list endpoint with filter for branch ${branchId}`);
    const listResponse = await axios.get(
      `${API_URL}/api/settings/modules/?school_branch=${branchId}`,
      { headers: { 'Authorization': `Bearer ${token}` } }
    );

    // Check for array response
    if (listResponse.data && Array.isArray(listResponse.data) && listResponse.data.length > 0) {
      const moduleActivation = listResponse.data[0];
      if (moduleActivation && typeof moduleActivation === 'object' && 'id' in moduleActivation) {
        return moduleActivation.id;
      }
    }

    // Check for paginated response
    if (listResponse.data && typeof listResponse.data === 'object' &&
        'results' in listResponse.data && Array.isArray(listResponse.data.results) &&
        listResponse.data.results.length > 0) {
      const moduleActivation = listResponse.data.results[0];
      if (moduleActivation && typeof moduleActivation === 'object' && 'id' in moduleActivation) {
        return moduleActivation.id;
      }
    }
  } catch (listError) {
    console.log("List API approach failed:", listError.message);
  }

  // Approach 3: Try the all modules endpoint and filter client-side
  try {
    console.log(`Trying all modules endpoint and filtering client-side for branch ${branchId}`);
    const allResponse = await axios.get(
      `${API_URL}/api/settings/modules/`,
      { headers: { 'Authorization': `Bearer ${token}` } }
    );

    // Check for array response
    if (allResponse.data && Array.isArray(allResponse.data)) {
      const moduleActivation = allResponse.data.find(item =>
        item && typeof item === 'object' &&
        'school_branch' in item && item.school_branch === branchId
      );

      if (moduleActivation && 'id' in moduleActivation) {
        return moduleActivation.id;
      }
    }

    // Check for paginated response
    if (allResponse.data && typeof allResponse.data === 'object' &&
        'results' in allResponse.data && Array.isArray(allResponse.data.results)) {
      const moduleActivation = allResponse.data.results.find(item =>
        item && typeof item === 'object' &&
        'school_branch' in item && item.school_branch === branchId
      );

      if (moduleActivation && 'id' in moduleActivation) {
        return moduleActivation.id;
      }
    }
  } catch (allError) {
    console.log("All modules approach failed:", allError.message);
  }

  // If all approaches fail, return null
  return null;
}

export default {
  getAvailableModules,
  getMyModules,
  updateModuleStatus,
  toggleModuleForBranch,
  isModuleVisible,
  fetchModulesForSettings,
  fetchBranches,
  fetchBranchModules,
  fetchBranchesModulesBatch,
  toggleModuleForBranchById
};
