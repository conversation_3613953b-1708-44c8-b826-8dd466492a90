import React from 'react';
import PageBreadcrumb from '../../components/common/PageBreadCrumb';
import PageMeta from '../../components/common/PageMeta';
import LicenseManager from '../../components/settings/LicenseManager';
import { useAuth } from '../../context/AuthContext';

const LicenseSettingsPage: React.FC = () => {
  const { user } = useAuth();
  const isSuperUser = user?.is_superuser || false;

  return (
    <>
      <PageMeta
        title="License Overview | ShuleXcel"
        description="View your ShuleXcel license details, status, and enabled modules."
      />
      <PageBreadcrumb pageTitle="License Overview" />

      <div className="container mx-auto px-4 py-6">
        <LicenseManager isSuperUser={isSuperUser} />
      </div>
    </>
  );
};

export default LicenseSettingsPage;
