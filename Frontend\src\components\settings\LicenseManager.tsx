import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import licenseService, { License, Package, Module } from '../../services/licenseService';
import statisticsService from '../../services/statisticsService';
import LicenseAnalytics from './LicenseAnalytics';

interface LicenseManagerProps {
  isSuperUser: boolean;
}

const LicenseManager: React.FC<LicenseManagerProps> = ({ isSuperUser }) => {
  const navigate = useNavigate();
  const [license, setLicense] = useState<License | null>(null);
  const [packages, setPackages] = useState<Package[]>([]);
  const [modules, setModules] = useState<Module[]>([]);
  const [studentCount, setStudentCount] = useState<number>(0);
  const [staffCount, setStaffCount] = useState<number>(0);
  const [branchCount, setBranchCount] = useState<number>(0);
  const [isLoading, setIsLoading] = useState(true);
  const [isStatsLoading, setIsStatsLoading] = useState(true);
  const [statsError, setStatsError] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchData = async () => {
      try {
        setIsLoading(true);
        setError(null);

        // Fetch license information
        const licenseData = await licenseService.getMyLicense();
        setLicense(licenseData);

        // Fetch packages and modules
        const [packagesData, modulesData] = await Promise.all([
          licenseService.getPackages(),
          licenseService.getModules()
        ]);

        setPackages(packagesData);
        setModules(modulesData);

        // Fetch statistics separately to handle potential errors
        setIsStatsLoading(true);
        try {
          const statistics = await statisticsService.getSchoolStatistics();
          setStudentCount(statistics.student_count);
          setStaffCount(statistics.staff_count);
          setBranchCount(statistics.branch_count);
          setStatsError(false);
        } catch (statsError) {
          console.error('Could not load statistics:', statsError);
          // Initialize with zeros instead of mock data
          setStudentCount(0);
          setStaffCount(0);
          setBranchCount(0);
          setStatsError(true);
        } finally {
          setIsStatsLoading(false);
        }
      } catch (err) {
        console.error('Error fetching license data:', err);
        setError('Failed to load license information. Please try again later.');
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, []);

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">
        <p>{error}</p>
      </div>
    );
  }

  if (!license) {
    return (
      <div className="bg-yellow-50 border border-yellow-200 text-yellow-700 px-4 py-3 rounded">
        <p>No license found for your school. Please contact your administrator.</p>
      </div>
    );
  }

  // Filter modules to show only those enabled in the license
  const enabledModules = modules.filter(module =>
    (license.enabled_modules && license.enabled_modules.includes(module.code)) || module.is_core
  );

  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="bg-white shadow rounded-lg p-6">
          <h2 className="text-xl font-semibold text-gray-800 mb-4">License Information</h2>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <p className="text-sm font-medium text-gray-500">License Key</p>
              <p className="text-base font-semibold">
                {license.formatted_license_key || 'Not Available'}
              </p>
            </div>

            <div>
              <p className="text-sm font-medium text-gray-500">Package</p>
              <p className="text-base font-semibold capitalize">
                {license.package_name || license.package_type || 'Not Available'}
              </p>
            </div>

            <div>
              <p className="text-sm font-medium text-gray-500">Status</p>
              <div className="flex items-center">
                <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                  license.is_active
                    ? 'bg-green-100 text-green-800'
                    : 'bg-red-100 text-red-800'
                }`}>
                  {license.subscription_status || 'Unknown'}
                </span>
                {license.is_active && license.expiry_date && (
                  <span className="ml-2 text-sm text-gray-500">
                    (Valid until {new Date(license.expiry_date).toLocaleDateString()})
                  </span>
                )}
              </div>
            </div>

            <div>
              <p className="text-sm font-medium text-gray-500">Created</p>
              <p className="text-base">
                {license.created_at ?
                  new Date(license.created_at).toLocaleDateString() :
                  'Not Available'
                }
              </p>
            </div>
          </div>
        </div>

        <LicenseAnalytics
          license={license}
          studentCount={studentCount}
          staffCount={staffCount}
          branchCount={branchCount}
          isStatsLoading={isStatsLoading}
          statsError={statsError}
        />
      </div>

      <div className="bg-white shadow rounded-lg p-6">
        <h2 className="text-xl font-semibold text-gray-800 mb-4">License Limits</h2>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
            <p className="text-sm font-medium text-gray-500">Maximum Students</p>
            <p className="text-2xl font-bold text-blue-600">
              {license.max_students || 'Unlimited'}
            </p>
            <p className="text-xs text-blue-500 mt-1">
              Current: {studentCount || 0}
            </p>
          </div>

          <div className="bg-green-50 p-4 rounded-lg border border-green-200">
            <p className="text-sm font-medium text-gray-500">Maximum Staff</p>
            <p className="text-2xl font-bold text-green-600">
              {license.max_staff || 'Unlimited'}
            </p>
            <p className="text-xs text-green-500 mt-1">
              Current: {staffCount || 0}
            </p>
          </div>

          <div className="bg-purple-50 p-4 rounded-lg border border-purple-200">
            <p className="text-sm font-medium text-gray-500">Maximum Branches</p>
            <p className="text-2xl font-bold text-purple-600">
              {license.max_branches || 'Unlimited'}
            </p>
            <p className="text-xs text-purple-500 mt-1">
              Current: {branchCount || 0}
            </p>
          </div>
        </div>
      </div>

      <div className="bg-white shadow rounded-lg p-6">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-semibold text-gray-800">Enabled Modules</h2>
          <span className="text-sm text-gray-500">
            {enabledModules.length} module{enabledModules.length !== 1 ? 's' : ''} enabled
          </span>
        </div>

        {enabledModules.length === 0 ? (
          <div className="text-center py-8">
            <div className="text-gray-400 mb-2">
              <svg className="mx-auto h-12 w-12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
              </svg>
            </div>
            <p className="text-gray-500">No modules are currently enabled for this license.</p>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {enabledModules.map(module => (
              <div key={module.code} className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                <div className="flex justify-between items-start mb-2">
                  <h3 className="text-lg font-medium text-gray-800">{module.name}</h3>
                  <div className="flex space-x-1">
                    {module.is_core && (
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                        Core
                      </span>
                    )}
                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                      Active
                    </span>
                  </div>
                </div>
                <p className="text-sm text-gray-500">{module.description}</p>
              </div>
            ))}
          </div>
        )}
      </div>

      <div className="mt-6 flex justify-end space-x-4">
        <button
          type="button"
          className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          onClick={() => navigate('/settings/license/analytics')}
        >
          View Detailed Analytics
        </button>
        {isSuperUser && (
          <button
            type="button"
            className="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
            onClick={() => navigate('/settings/license/manage')}
          >
            Manage All Licenses
          </button>
        )}
      </div>
    </div>
  );
};

export default LicenseManager;
