from django.urls import path, include
from rest_framework.routers import Default<PERSON>outer
from .views import (
    SchoolProfileViewSet, SystemConfigViewSet, PermissionViewSet,
    RolePermissionViewSet, UserManagementViewSet, BackupRestoreViewSet,
    IntegrationViewSet, AuditLogViewSet, LicenseViewSet
)

router = DefaultRouter()
router.register('school-profiles', SchoolProfileViewSet, basename='school-profile')
router.register('system-config', SystemConfigViewSet, basename='system-config')
router.register('permissions', PermissionViewSet, basename='permission')
router.register('roles', RolePermissionViewSet, basename='role')
router.register('users', UserManagementViewSet, basename='user')
router.register('backups', BackupRestoreViewSet, basename='backup')
router.register('integrations', IntegrationViewSet, basename='integration')
router.register('audit-logs', AuditLogViewSet, basename='audit-log')
router.register('licenses', LicenseViewSet, basename='license')

urlpatterns = [
    path('', include(router.urls)),
]
