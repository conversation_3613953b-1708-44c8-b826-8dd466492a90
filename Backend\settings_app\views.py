from rest_framework import viewsets, status, filters, permissions
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import Is<PERSON><PERSON><PERSON><PERSON>ted, IsAdminUser
from django.utils import timezone
from django.db.models import Q
import logging

from .models import (
    SchoolProfile, SystemConfiguration, Permission, Role, UserRole,
    BackupConfiguration, Backup, Integration, AuditLog
)
from .license_models import LicenseSubscription
from .serializers import (
    SchoolProfileSerializer, SystemConfigurationSerializer, PermissionSerializer, RoleSerializer, UserRoleSerializer,
    BackupConfigurationSerializer, BackupSerializer, IntegrationSerializer, AuditLogSerializer, LicenseSubscriptionSerializer
)
from core.permissions import BranchBasedPermission, IsAdminOrSuperUser, IsSuperUserOrSchoolAdmin

logger = logging.getLogger(__name__)

class SchoolProfileViewSet(viewsets.ModelViewSet):
    serializer_class = SchoolProfileSerializer
    permission_classes = [IsAuthenticated, IsAdminOrSuperUser]

    def get_queryset(self):
        queryset = SchoolProfile.objects.all()

        # Filter by school branch
        if not self.request.user.is_superuser:
            queryset = queryset.filter(school_branch=self.request.user.school_branch)

        return queryset

    def perform_create(self, serializer):
        serializer.save(
            updated_by=self.request.user,
            school_branch=self.request.user.school_branch
        )

    def perform_update(self, serializer):
        serializer.save(updated_by=self.request.user)

    @action(detail=False, methods=['get'])
    def current(self, request):
        """Get the profile for the current user's school branch"""
        try:
            profile = SchoolProfile.objects.get(school_branch=request.user.school_branch)
            serializer = self.get_serializer(profile)
            return Response(serializer.data)
        except SchoolProfile.DoesNotExist:
            return Response(
                {"detail": "School profile not found for your branch."},
                status=status.HTTP_404_NOT_FOUND
            )

class BranchManagementViewSet(viewsets.ViewSet):
    permission_classes = [IsAuthenticated, IsAdminUser]

    def list(self, request):
        # This would be implemented to list branches
        # For now, we'll return a placeholder
        return Response({"detail": "Branch management functionality to be implemented"})

class SystemConfigViewSet(viewsets.ModelViewSet):
    serializer_class = SystemConfigurationSerializer
    permission_classes = [IsAuthenticated, IsAdminOrSuperUser]

    def get_queryset(self):
        queryset = SystemConfiguration.objects.all()

        # Filter by school branch
        if not self.request.user.is_superuser:
            queryset = queryset.filter(school_branch=self.request.user.school_branch)

        return queryset

    def perform_create(self, serializer):
        serializer.save(
            updated_by=self.request.user,
            school_branch=self.request.user.school_branch
        )

    def perform_update(self, serializer):
        serializer.save(updated_by=self.request.user)

    @action(detail=False, methods=['get'])
    def current(self, request):
        """Get the system configuration for the current user's school branch"""
        try:
            config = SystemConfiguration.objects.get(school_branch=request.user.school_branch)
            serializer = self.get_serializer(config)
            return Response(serializer.data)
        except SystemConfiguration.DoesNotExist:
            return Response(
                {"detail": "System configuration not found for your branch."},
                status=status.HTTP_404_NOT_FOUND
            )

    @action(detail=True, methods=['post'])
    def toggle_maintenance_mode(self, request, pk=None):
        config = self.get_object()
        config.system_maintenance_mode = not config.system_maintenance_mode
        config.updated_by = request.user
        config.save()

        serializer = self.get_serializer(config)
        return Response(serializer.data)

class PermissionViewSet(viewsets.ModelViewSet):
    serializer_class = PermissionSerializer
    permission_classes = [IsAuthenticated, IsAdminOrSuperUser]
    filter_backends = [filters.SearchFilter, filters.OrderingFilter]
    search_fields = ['name', 'code', 'module', 'action', 'description']
    ordering_fields = ['name', 'module', 'action', 'is_active']
    ordering = ['module', 'action']

    def get_queryset(self):
        queryset = Permission.objects.all()

        # Filter by module
        module = self.request.query_params.get('module')
        if module:
            queryset = queryset.filter(module=module)

        # Filter by action
        action = self.request.query_params.get('action')
        if action:
            queryset = queryset.filter(action=action)

        # Filter by active status
        is_active = self.request.query_params.get('is_active')
        if is_active is not None:
            queryset = queryset.filter(is_active=(is_active.lower() == 'true'))

        return queryset

class RolePermissionViewSet(viewsets.ModelViewSet):
    serializer_class = RoleSerializer
    permission_classes = [IsAuthenticated, IsAdminOrSuperUser]
    filter_backends = [filters.SearchFilter, filters.OrderingFilter]
    search_fields = ['name', 'description']
    ordering_fields = ['name', 'is_active', 'created_at']
    ordering = ['name']

    def get_queryset(self):
        queryset = Role.objects.all()

        # Filter by school branch
        if not self.request.user.is_superuser:
            queryset = queryset.filter(school_branch=self.request.user.school_branch)

        # Filter by active status
        is_active = self.request.query_params.get('is_active')
        if is_active is not None:
            queryset = queryset.filter(is_active=(is_active.lower() == 'true'))

        # Filter by system role status
        is_system_role = self.request.query_params.get('is_system_role')
        if is_system_role is not None:
            queryset = queryset.filter(is_system_role=(is_system_role.lower() == 'true'))

        return queryset

    def perform_create(self, serializer):
        serializer.save(
            created_by=self.request.user,
            school_branch=self.request.user.school_branch
        )

    @action(detail=True, methods=['get'])
    def users(self, request, pk=None):
        role = self.get_object()
        user_roles = UserRole.objects.filter(role=role)
        serializer = UserRoleSerializer(user_roles, many=True)
        return Response(serializer.data)

    @action(detail=True, methods=['post'])
    def assign_to_user(self, request, pk=None):
        role = self.get_object()
        user_id = request.data.get('user_id')
        is_primary = request.data.get('is_primary', False)

        if not user_id:
            return Response(
                {"detail": "User ID is required."},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Create or update the user role
        user_role, created = UserRole.objects.update_or_create(
            user_id=user_id,
            role=role,
            school_branch=request.user.school_branch,
            defaults={
                'is_primary': is_primary,
                'assigned_by': request.user,
                'assigned_at': timezone.now()
            }
        )

        serializer = UserRoleSerializer(user_role)
        return Response(serializer.data)

    @action(detail=True, methods=['post'])
    def remove_from_user(self, request, pk=None):
        role = self.get_object()
        user_id = request.data.get('user_id')

        if not user_id:
            return Response(
                {"detail": "User ID is required."},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Delete the user role
        try:
            user_role = UserRole.objects.get(
                user_id=user_id,
                role=role,
                school_branch=request.user.school_branch
            )
            user_role.delete()
            return Response({"detail": "Role removed from user successfully."})
        except UserRole.DoesNotExist:
            return Response(
                {"detail": "User does not have this role."},
                status=status.HTTP_404_NOT_FOUND
            )

class UserManagementViewSet(viewsets.ViewSet):
    permission_classes = [IsAuthenticated, IsAdminOrSuperUser]

    def list(self, request):
        # This would be implemented to list and manage users
        # For now, we'll return a placeholder
        return Response({"detail": "User management functionality to be implemented"})

class BackupRestoreViewSet(viewsets.ModelViewSet):
    serializer_class = BackupSerializer
    permission_classes = [IsAuthenticated, IsAdminOrSuperUser]
    filter_backends = [filters.SearchFilter, filters.OrderingFilter]
    search_fields = ['backup_name', 'backup_type', 'status']
    ordering_fields = ['started_at', 'status', 'backup_type']
    ordering = ['-started_at']

    def get_queryset(self):
        queryset = Backup.objects.all()

        # Filter by school branch
        if not self.request.user.is_superuser:
            queryset = queryset.filter(school_branch=self.request.user.school_branch)

        # Filter by backup type
        backup_type = self.request.query_params.get('backup_type')
        if backup_type:
            queryset = queryset.filter(backup_type=backup_type)

        # Filter by status
        status_param = self.request.query_params.get('status')
        if status_param:
            queryset = queryset.filter(status=status_param)

        # Filter by date range
        start_date = self.request.query_params.get('start_date')
        end_date = self.request.query_params.get('end_date')
        if start_date and end_date:
            queryset = queryset.filter(started_at__date__range=[start_date, end_date])

        return queryset

    def perform_create(self, serializer):
        serializer.save(
            created_by=self.request.user,
            school_branch=self.request.user.school_branch
        )

    @action(detail=False, methods=['get'])
    def configuration(self, request):
        """Get the backup configuration for the current user's school branch"""
        try:
            config = BackupConfiguration.objects.get(school_branch=request.user.school_branch)
            serializer = BackupConfigurationSerializer(config)
            return Response(serializer.data)
        except BackupConfiguration.DoesNotExist:
            return Response(
                {"detail": "Backup configuration not found for your branch."},
                status=status.HTTP_404_NOT_FOUND
            )

    @action(detail=False, methods=['post'])
    def update_configuration(self, request):
        """Update the backup configuration for the current user's school branch"""
        try:
            config = BackupConfiguration.objects.get(school_branch=request.user.school_branch)
            serializer = BackupConfigurationSerializer(config, data=request.data, partial=True)
            if serializer.is_valid():
                serializer.save(updated_by=request.user)
                return Response(serializer.data)
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
        except BackupConfiguration.DoesNotExist:
            # Create a new configuration
            serializer = BackupConfigurationSerializer(data=request.data)
            if serializer.is_valid():
                serializer.save(
                    updated_by=request.user,
                    school_branch=request.user.school_branch
                )
                return Response(serializer.data, status=status.HTTP_201_CREATED)
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=False, methods=['post'])
    def create_backup(self, request):
        """Create a new backup"""
        backup_name = request.data.get('backup_name', f"Backup_{timezone.now().strftime('%Y%m%d_%H%M%S')}")
        backup_type = request.data.get('backup_type', 'MANUAL')

        # Here you would implement the actual backup creation logic
        # For now, we'll just create a record

        backup = Backup.objects.create(
            school_branch=request.user.school_branch,
            backup_name=backup_name,
            backup_type=backup_type,
            status='IN_PROGRESS',
            created_by=request.user
        )

        serializer = self.get_serializer(backup)
        return Response(serializer.data, status=status.HTTP_201_CREATED)

    @action(detail=True, methods=['post'])
    def restore(self, request, pk=None):
        """Restore from a backup"""
        backup = self.get_object()

        if backup.status != 'COMPLETED':
            return Response(
                {"detail": "Can only restore from completed backups."},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Here you would implement the actual restore logic
        # For now, we'll just return a success message

        return Response({"detail": "Restore process initiated."})

class IntegrationViewSet(viewsets.ModelViewSet):
    serializer_class = IntegrationSerializer
    permission_classes = [IsAuthenticated, IsAdminOrSuperUser]
    filter_backends = [filters.SearchFilter, filters.OrderingFilter]
    search_fields = ['name', 'provider', 'integration_type']
    ordering_fields = ['name', 'integration_type', 'status', 'created_at']
    ordering = ['name']

    def get_queryset(self):
        queryset = Integration.objects.all()

        # Filter by school branch
        if not self.request.user.is_superuser:
            queryset = queryset.filter(school_branch=self.request.user.school_branch)

        # Filter by integration type
        integration_type = self.request.query_params.get('integration_type')
        if integration_type:
            queryset = queryset.filter(integration_type=integration_type)

        # Filter by status
        status_param = self.request.query_params.get('status')
        if status_param:
            queryset = queryset.filter(status=status_param)

        # Filter by enabled status
        is_enabled = self.request.query_params.get('is_enabled')
        if is_enabled is not None:
            queryset = queryset.filter(is_enabled=(is_enabled.lower() == 'true'))

        return queryset

    def perform_create(self, serializer):
        serializer.save(
            created_by=self.request.user,
            school_branch=self.request.user.school_branch
        )

    @action(detail=True, methods=['post'])
    def toggle_status(self, request, pk=None):
        integration = self.get_object()
        integration.is_enabled = not integration.is_enabled
        integration.status = 'ACTIVE' if integration.is_enabled else 'INACTIVE'
        integration.save()

        serializer = self.get_serializer(integration)
        return Response(serializer.data)

    @action(detail=True, methods=['post'])
    def test_connection(self, request, pk=None):
        integration = self.get_object()

        # Here you would implement the actual connection testing logic
        # For now, we'll just return a success message

        return Response({"detail": "Connection test successful."})

class AuditLogViewSet(viewsets.ReadOnlyModelViewSet):
    serializer_class = AuditLogSerializer
    permission_classes = [IsAuthenticated, IsAdminOrSuperUser]
    filter_backends = [filters.SearchFilter, filters.OrderingFilter]
    search_fields = ['user__username', 'user__email', 'action', 'entity_type', 'entity_name']
    ordering_fields = ['timestamp', 'action', 'entity_type']
    ordering = ['-timestamp']

    def get_queryset(self):
        queryset = AuditLog.objects.all()

        # Filter by school branch
        if not self.request.user.is_superuser:
            queryset = queryset.filter(school_branch=self.request.user.school_branch)

        # Filter by user
        user_id = self.request.query_params.get('user_id')
        if user_id:
            queryset = queryset.filter(user_id=user_id)

        # Filter by action
        action = self.request.query_params.get('action')
        if action:
            queryset = queryset.filter(action=action)

        # Filter by entity type
        entity_type = self.request.query_params.get('entity_type')
        if entity_type:
            queryset = queryset.filter(entity_type=entity_type)

        # Filter by date range
        start_date = self.request.query_params.get('start_date')
        end_date = self.request.query_params.get('end_date')
        if start_date and end_date:
            queryset = queryset.filter(timestamp__date__range=[start_date, end_date])

        return queryset

    @action(detail=False, methods=['post'])
    def log_action(self, request):
        """Create a new audit log entry"""
        action = request.data.get('action')
        entity_type = request.data.get('entity_type')
        entity_id = request.data.get('entity_id')
        entity_name = request.data.get('entity_name')
        action_details = request.data.get('action_details')

        if not action or not entity_type:
            return Response(
                {"detail": "Action and entity_type are required."},
                status=status.HTTP_400_BAD_REQUEST
            )

        log = AuditLog.objects.create(
            user=request.user,
            action=action,
            entity_type=entity_type,
            entity_id=entity_id,
            entity_name=entity_name,
            action_details=action_details,
            ip_address=self.get_client_ip(request),
            user_agent=request.META.get('HTTP_USER_AGENT', ''),
            school_branch=request.user.school_branch
        )

        serializer = self.get_serializer(log)
        return Response(serializer.data, status=status.HTTP_201_CREATED)

    def get_client_ip(self, request):
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip

class LicenseViewSet(viewsets.ModelViewSet):
    """
    API endpoint for managing license subscriptions
    """
    queryset = LicenseSubscription.objects.all()
    serializer_class = LicenseSubscriptionSerializer
    permission_classes = [permissions.IsAuthenticated, IsSuperUserOrSchoolAdmin]

    def get_queryset(self):
        user = self.request.user
        if user.is_superuser:
            return self.queryset

        # School admins can only see their own school's license
        if hasattr(user, 'school_branch') and user.school_branch:
            return self.queryset.filter(school=user.school_branch.school)

        return self.queryset.none()

    @action(detail=False, methods=['get'])
    def my_license(self, request):
        """Get the license for the current user's school"""
        try:
            user = request.user

            # For super users, return a special response indicating they can manage all licenses
            if user.is_superuser:
                # Check if there's a school_id parameter for super users
                school_id = request.query_params.get('school_id')
                if school_id:
                    try:
                        license_sub = LicenseSubscription.objects.get(school_id=school_id)
                        serializer = self.get_serializer(license_sub)
                        return Response(serializer.data)
                    except LicenseSubscription.DoesNotExist:
                        return Response(
                            {"error": f"No license found for school ID {school_id}"},
                            status=status.HTTP_404_NOT_FOUND
                        )
                else:
                    # Return a message for super users without specific school context
                    return Response(
                        {
                            "message": "Super user access - no specific school license. Use school_id parameter to get specific school license.",
                            "is_superuser": True,
                            "total_licenses": LicenseSubscription.objects.count()
                        },
                        status=status.HTTP_200_OK
                    )

            if not hasattr(user, 'school_branch') or not user.school_branch:
                return Response(
                    {"error": "You are not associated with any school branch"},
                    status=status.HTTP_404_NOT_FOUND
                )

            if not hasattr(user.school_branch, 'school'):
                return Response(
                    {"error": "Your school branch is not associated with any school"},
                    status=status.HTTP_404_NOT_FOUND
                )

            try:
                license_sub = LicenseSubscription.objects.get(school=user.school_branch.school)
                serializer = self.get_serializer(license_sub)
                return Response(serializer.data)
            except LicenseSubscription.DoesNotExist:
                # Create a default license if none exists
                default_license = LicenseSubscription.objects.create(
                    school=user.school_branch.school,
                    package_type='basic',  # or whatever default package you want
                    subscription_status='active',
                    max_users=10,  # or whatever default value you want
                    features={}  # or whatever default features you want
                )
                serializer = self.get_serializer(default_license)
                return Response(serializer.data, status=status.HTTP_201_CREATED)
        except Exception as e:
            logger.error(f"Error in my_license: {str(e)}", exc_info=True)
            return Response(
                {"error": "An error occurred while fetching your license"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=False, methods=['get'])
    def packages(self, request):
        """Get available package tiers"""
        from .modules import PACKAGE_TIERS
        packages = []
        for code, info in PACKAGE_TIERS.items():
            packages.append({
                'code': code,
                'name': info.get('name', code),
                'description': info.get('description', ''),
                'modules': info.get('modules', [])
            })

        return Response(packages)

    @action(detail=False, methods=['get'])
    def modules(self, request):
        """Get available modules"""
        from .modules import AVAILABLE_MODULES, MODULE_STATUS
        modules = []
        for code, info in AVAILABLE_MODULES.items():
            modules.append({
                'code': code,
                'name': info.get('name', code),
                'description': info.get('description', ''),
                'is_core': info.get('is_core', False),
                'dependencies': info.get('dependencies', []),
                'app_names': info.get('app_names', []),
                'status': info.get('status', MODULE_STATUS['DEVELOPMENT'])
            })

        return Response(modules)